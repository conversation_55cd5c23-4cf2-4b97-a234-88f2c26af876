{"version": 3, "sources": ["../../splitpanes/dist/splitpanes.es.js"], "sourcesContent": ["import { useSlots as ce, ref as k, computed as z, watch as B, onMounted as G, onBeforeUnmount as X, provide as g, createBlock as ve, openBlock as Y, resolveDynamicComponent as me, nextTick as T, h as fe, inject as w, getCurrentInstance as de, create<PERSON>lement<PERSON><PERSON> as pe, normalize<PERSON><PERSON><PERSON> as ze, unref as he, renderSlot as xe } from \"vue\";\nconst Pe = {\n  __name: \"splitpanes\",\n  props: {\n    horizontal: { type: Boolean, default: !1 },\n    pushOtherPanes: { type: Boolean, default: !0 },\n    maximizePanes: { type: Boolean, default: !0 },\n    // Maximize pane on splitter double click/tap.\n    rtl: { type: Boolean, default: !1 },\n    // Right to left direction.\n    firstSplitter: { type: Boolean, default: !1 }\n  },\n  emits: [\n    \"ready\",\n    \"resize\",\n    \"resized\",\n    \"pane-click\",\n    \"pane-maximize\",\n    \"pane-add\",\n    \"pane-remove\",\n    \"splitter-click\",\n    \"splitter-dblclick\"\n  ],\n  setup(D, { emit: h }) {\n    const y = h, u = D, E = ce(), l = k([]), M = z(() => l.value.reduce((e, n) => (e[~~n.id] = n) && e, {})), m = z(() => l.value.length), x = k(null), S = k(!1), c = k({\n      mouseDown: !1,\n      dragging: !1,\n      activeSplitter: null,\n      cursorOffset: 0\n      // Cursor offset within the splitter.\n    }), f = k({\n      // Used to detect double click on touch devices.\n      splitter: null,\n      timeoutId: null\n    }), _ = z(() => ({\n      [`splitpanes splitpanes--${u.horizontal ? \"horizontal\" : \"vertical\"}`]: !0,\n      \"splitpanes--dragging\": c.value.dragging\n    })), R = () => {\n      document.addEventListener(\"mousemove\", r, { passive: !1 }), document.addEventListener(\"mouseup\", P), \"ontouchstart\" in window && (document.addEventListener(\"touchmove\", r, { passive: !1 }), document.addEventListener(\"touchend\", P));\n    }, O = () => {\n      document.removeEventListener(\"mousemove\", r, { passive: !1 }), document.removeEventListener(\"mouseup\", P), \"ontouchstart\" in window && (document.removeEventListener(\"touchmove\", r, { passive: !1 }), document.removeEventListener(\"touchend\", P));\n    }, b = (e, n) => {\n      const t = e.target.closest(\".splitpanes__splitter\");\n      if (t) {\n        const { left: i, top: a } = t.getBoundingClientRect(), { clientX: s, clientY: o } = \"ontouchstart\" in window && e.touches ? e.touches[0] : e;\n        c.value.cursorOffset = u.horizontal ? o - a : s - i;\n      }\n      R(), c.value.mouseDown = !0, c.value.activeSplitter = n;\n    }, r = (e) => {\n      c.value.mouseDown && (e.preventDefault(), c.value.dragging = !0, requestAnimationFrame(() => {\n        K(I(e)), d(\"resize\", { event: e }, !0);\n      }));\n    }, P = (e) => {\n      c.value.dragging && (window.getSelection().removeAllRanges(), d(\"resized\", { event: e }, !0)), c.value.mouseDown = !1, c.value.activeSplitter = null, setTimeout(() => {\n        c.value.dragging = !1, O();\n      }, 100);\n    }, A = (e, n) => {\n      \"ontouchstart\" in window && (e.preventDefault(), f.value.splitter === n ? (clearTimeout(f.value.timeoutId), f.value.timeoutId = null, U(e, n), f.value.splitter = null) : (f.value.splitter = n, f.value.timeoutId = setTimeout(() => f.value.splitter = null, 500))), c.value.dragging || d(\"splitter-click\", { event: e, index: n }, !0);\n    }, U = (e, n) => {\n      if (d(\"splitter-dblclick\", { event: e, index: n }, !0), u.maximizePanes) {\n        let t = 0;\n        l.value = l.value.map((i, a) => (i.size = a === n ? i.max : i.min, a !== n && (t += i.min), i)), l.value[n].size -= t, d(\"pane-maximize\", { event: e, index: n, pane: l.value[n] }), d(\"resized\", { event: e, index: n }, !0);\n      }\n    }, W = (e, n) => {\n      d(\"pane-click\", {\n        event: e,\n        index: M.value[n].index,\n        pane: M.value[n]\n      });\n    }, I = (e) => {\n      const n = x.value.getBoundingClientRect(), { clientX: t, clientY: i } = \"ontouchstart\" in window && e.touches ? e.touches[0] : e;\n      return {\n        x: t - (u.horizontal ? 0 : c.value.cursorOffset) - n.left,\n        y: i - (u.horizontal ? c.value.cursorOffset : 0) - n.top\n      };\n    }, J = (e) => {\n      e = e[u.horizontal ? \"y\" : \"x\"];\n      const n = x.value[u.horizontal ? \"clientHeight\" : \"clientWidth\"];\n      return u.rtl && !u.horizontal && (e = n - e), e * 100 / n;\n    }, K = (e) => {\n      const n = c.value.activeSplitter;\n      let t = {\n        prevPanesSize: $(n),\n        nextPanesSize: N(n),\n        prevReachedMinPanes: 0,\n        nextReachedMinPanes: 0\n      };\n      const i = 0 + (u.pushOtherPanes ? 0 : t.prevPanesSize), a = 100 - (u.pushOtherPanes ? 0 : t.nextPanesSize), s = Math.max(Math.min(J(e), a), i);\n      let o = [n, n + 1], v = l.value[o[0]] || null, p = l.value[o[1]] || null;\n      const H = v.max < 100 && s >= v.max + t.prevPanesSize, ue = p.max < 100 && s <= 100 - (p.max + N(n + 1));\n      if (H || ue) {\n        H ? (v.size = v.max, p.size = Math.max(100 - v.max - t.prevPanesSize - t.nextPanesSize, 0)) : (v.size = Math.max(100 - p.max - t.prevPanesSize - N(n + 1), 0), p.size = p.max);\n        return;\n      }\n      if (u.pushOtherPanes) {\n        const j = Q(t, s);\n        if (!j) return;\n        ({ sums: t, panesToResize: o } = j), v = l.value[o[0]] || null, p = l.value[o[1]] || null;\n      }\n      v !== null && (v.size = Math.min(Math.max(s - t.prevPanesSize - t.prevReachedMinPanes, v.min), v.max)), p !== null && (p.size = Math.min(Math.max(100 - s - t.nextPanesSize - t.nextReachedMinPanes, p.min), p.max));\n    }, Q = (e, n) => {\n      const t = c.value.activeSplitter, i = [t, t + 1];\n      return n < e.prevPanesSize + l.value[i[0]].min && (i[0] = V(t).index, e.prevReachedMinPanes = 0, i[0] < t && l.value.forEach((a, s) => {\n        s > i[0] && s <= t && (a.size = a.min, e.prevReachedMinPanes += a.min);\n      }), e.prevPanesSize = $(i[0]), i[0] === void 0) ? (e.prevReachedMinPanes = 0, l.value[0].size = l.value[0].min, l.value.forEach((a, s) => {\n        s > 0 && s <= t && (a.size = a.min, e.prevReachedMinPanes += a.min);\n      }), l.value[i[1]].size = 100 - e.prevReachedMinPanes - l.value[0].min - e.prevPanesSize - e.nextPanesSize, null) : n > 100 - e.nextPanesSize - l.value[i[1]].min && (i[1] = Z(t).index, e.nextReachedMinPanes = 0, i[1] > t + 1 && l.value.forEach((a, s) => {\n        s > t && s < i[1] && (a.size = a.min, e.nextReachedMinPanes += a.min);\n      }), e.nextPanesSize = N(i[1] - 1), i[1] === void 0) ? (e.nextReachedMinPanes = 0, l.value.forEach((a, s) => {\n        s < m.value - 1 && s >= t + 1 && (a.size = a.min, e.nextReachedMinPanes += a.min);\n      }), l.value[i[0]].size = 100 - e.prevPanesSize - N(i[0] - 1), null) : { sums: e, panesToResize: i };\n    }, $ = (e) => l.value.reduce((n, t, i) => n + (i < e ? t.size : 0), 0), N = (e) => l.value.reduce((n, t, i) => n + (i > e + 1 ? t.size : 0), 0), V = (e) => [...l.value].reverse().find((t) => t.index < e && t.size > t.min) || {}, Z = (e) => l.value.find((t) => t.index > e + 1 && t.size > t.min) || {}, ee = () => {\n      var n;\n      const e = Array.from(((n = x.value) == null ? void 0 : n.children) || []);\n      for (const t of e) {\n        const i = t.classList.contains(\"splitpanes__pane\"), a = t.classList.contains(\"splitpanes__splitter\");\n        !i && !a && (t.remove(), console.warn(\"Splitpanes: Only <pane> elements are allowed at the root of <splitpanes>. One of your DOM nodes was removed.\"));\n      }\n    }, F = (e, n, t = !1) => {\n      const i = e - 1, a = document.createElement(\"div\");\n      a.classList.add(\"splitpanes__splitter\"), t || (a.onmousedown = (s) => b(s, i), typeof window < \"u\" && \"ontouchstart\" in window && (a.ontouchstart = (s) => b(s, i)), a.onclick = (s) => A(s, i + 1)), a.ondblclick = (s) => U(s, i + 1), n.parentNode.insertBefore(a, n);\n    }, ne = (e) => {\n      e.onmousedown = void 0, e.onclick = void 0, e.ondblclick = void 0, e.remove();\n    }, C = () => {\n      var t;\n      const e = Array.from(((t = x.value) == null ? void 0 : t.children) || []);\n      for (const i of e)\n        i.className.includes(\"splitpanes__splitter\") && ne(i);\n      let n = 0;\n      for (const i of e)\n        i.className.includes(\"splitpanes__pane\") && (!n && u.firstSplitter ? F(n, i, !0) : n && F(n, i), n++);\n    }, ie = ({ uid: e, ...n }) => {\n      const t = M.value[e];\n      for (const [i, a] of Object.entries(n)) t[i] = a;\n    }, te = (e) => {\n      var t;\n      let n = -1;\n      Array.from(((t = x.value) == null ? void 0 : t.children) || []).some((i) => (i.className.includes(\"splitpanes__pane\") && n++, i.isSameNode(e.el))), l.value.splice(n, 0, { ...e, index: n }), l.value.forEach((i, a) => i.index = a), S.value && T(() => {\n        C(), L({ addedPane: l.value[n] }), d(\"pane-add\", { pane: l.value[n] });\n      });\n    }, ae = (e) => {\n      const n = l.value.findIndex((i) => i.id === e);\n      l.value[n].el = null;\n      const t = l.value.splice(n, 1)[0];\n      l.value.forEach((i, a) => i.index = a), T(() => {\n        C(), d(\"pane-remove\", { pane: t }), L({ removedPane: { ...t } });\n      });\n    }, L = (e = {}) => {\n      !e.addedPane && !e.removedPane ? le() : l.value.some((n) => n.givenSize !== null || n.min || n.max < 100) ? oe(e) : se(), S.value && d(\"resized\");\n    }, se = () => {\n      const e = 100 / m.value;\n      let n = 0;\n      const t = [], i = [];\n      for (const a of l.value)\n        a.size = Math.max(Math.min(e, a.max), a.min), n -= a.size, a.size >= a.max && t.push(a.id), a.size <= a.min && i.push(a.id);\n      n > 0.1 && q(n, t, i);\n    }, le = () => {\n      let e = 100;\n      const n = [], t = [];\n      let i = 0;\n      for (const s of l.value)\n        e -= s.size, s.givenSize !== null && i++, s.size >= s.max && n.push(s.id), s.size <= s.min && t.push(s.id);\n      let a = 100;\n      if (e > 0.1) {\n        for (const s of l.value)\n          s.givenSize === null && (s.size = Math.max(Math.min(e / (m.value - i), s.max), s.min)), a -= s.size;\n        a > 0.1 && q(a, n, t);\n      }\n    }, oe = ({ addedPane: e, removedPane: n } = {}) => {\n      let t = 100 / m.value, i = 0;\n      const a = [], s = [];\n      ((e == null ? void 0 : e.givenSize) ?? null) !== null && (t = (100 - e.givenSize) / (m.value - 1));\n      for (const o of l.value)\n        i -= o.size, o.size >= o.max && a.push(o.id), o.size <= o.min && s.push(o.id);\n      if (!(Math.abs(i) < 0.1)) {\n        for (const o of l.value)\n          (e == null ? void 0 : e.givenSize) !== null && (e == null ? void 0 : e.id) === o.id || (o.size = Math.max(Math.min(t, o.max), o.min)), i -= o.size, o.size >= o.max && a.push(o.id), o.size <= o.min && s.push(o.id);\n        i > 0.1 && q(i, a, s);\n      }\n    }, q = (e, n, t) => {\n      let i;\n      e > 0 ? i = e / (m.value - n.length) : i = e / (m.value - t.length), l.value.forEach((a, s) => {\n        if (e > 0 && !n.includes(a.id)) {\n          const o = Math.max(Math.min(a.size + i, a.max), a.min), v = o - a.size;\n          e -= v, a.size = o;\n        } else if (!t.includes(a.id)) {\n          const o = Math.max(Math.min(a.size + i, a.max), a.min), v = o - a.size;\n          e -= v, a.size = o;\n        }\n      }), Math.abs(e) > 0.1 && T(() => {\n        S.value && console.warn(\"Splitpanes: Could not resize panes correctly due to their constraints.\");\n      });\n    }, d = (e, n = void 0, t = !1) => {\n      const i = (n == null ? void 0 : n.index) ?? c.value.activeSplitter ?? null;\n      y(e, {\n        ...n,\n        ...i !== null && { index: i },\n        ...t && i !== null && {\n          prevPane: l.value[i - (u.firstSplitter ? 1 : 0)],\n          nextPane: l.value[i + (u.firstSplitter ? 0 : 1)]\n        },\n        panes: l.value.map((a) => ({ min: a.min, max: a.max, size: a.size }))\n      });\n    };\n    B(() => u.firstSplitter, () => C()), G(() => {\n      ee(), C(), L(), d(\"ready\"), S.value = !0;\n    }), X(() => S.value = !1);\n    const re = () => {\n      var e;\n      return fe(\n        \"div\",\n        { ref: x, class: _.value },\n        (e = E.default) == null ? void 0 : e.call(E)\n      );\n    };\n    return g(\"panes\", l), g(\"indexedPanes\", M), g(\"horizontal\", z(() => u.horizontal)), g(\"requestUpdate\", ie), g(\"onPaneAdd\", te), g(\"onPaneRemove\", ae), g(\"onPaneClick\", W), (e, n) => (Y(), ve(me(re)));\n  }\n}, ge = {\n  __name: \"pane\",\n  props: {\n    size: { type: [Number, String] },\n    minSize: { type: [Number, String], default: 0 },\n    maxSize: { type: [Number, String], default: 100 }\n  },\n  setup(D) {\n    var b;\n    const h = D, y = w(\"requestUpdate\"), u = w(\"onPaneAdd\"), E = w(\"horizontal\"), l = w(\"onPaneRemove\"), M = w(\"onPaneClick\"), m = (b = de()) == null ? void 0 : b.uid, x = w(\"indexedPanes\"), S = z(() => x.value[m]), c = k(null), f = z(() => {\n      const r = isNaN(h.size) || h.size === void 0 ? 0 : parseFloat(h.size);\n      return Math.max(Math.min(r, R.value), _.value);\n    }), _ = z(() => {\n      const r = parseFloat(h.minSize);\n      return isNaN(r) ? 0 : r;\n    }), R = z(() => {\n      const r = parseFloat(h.maxSize);\n      return isNaN(r) ? 100 : r;\n    }), O = z(() => {\n      var r;\n      return `${E.value ? \"height\" : \"width\"}: ${(r = S.value) == null ? void 0 : r.size}%`;\n    });\n    return B(() => f.value, (r) => y({ uid: m, size: r })), B(() => _.value, (r) => y({ uid: m, min: r })), B(() => R.value, (r) => y({ uid: m, max: r })), G(() => {\n      u({\n        id: m,\n        el: c.value,\n        min: _.value,\n        max: R.value,\n        // The given size (useful to know the user intention).\n        givenSize: h.size === void 0 ? null : f.value,\n        size: f.value\n        // The computed current size at any time.\n      });\n    }), X(() => l(m)), (r, P) => (Y(), pe(\"div\", {\n      ref_key: \"paneEl\",\n      ref: c,\n      class: \"splitpanes__pane\",\n      onClick: P[0] || (P[0] = (A) => he(M)(A, r._.uid)),\n      style: ze(O.value)\n    }, [\n      xe(r.$slots, \"default\")\n    ], 4));\n  }\n};\nexport {\n  ge as Pane,\n  Pe as Splitpanes\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAM,KAAK;AAAA,EACT,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,YAAY,EAAE,MAAM,SAAS,SAAS,MAAG;AAAA,IACzC,gBAAgB,EAAE,MAAM,SAAS,SAAS,KAAG;AAAA,IAC7C,eAAe,EAAE,MAAM,SAAS,SAAS,KAAG;AAAA;AAAA,IAE5C,KAAK,EAAE,MAAM,SAAS,SAAS,MAAG;AAAA;AAAA,IAElC,eAAe,EAAE,MAAM,SAAS,SAAS,MAAG;AAAA,EAC9C;AAAA,EACA,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,GAAG,EAAE,MAAMA,GAAE,GAAG;AACpB,UAAM,IAAIA,IAAG,IAAI,GAAG,IAAI,SAAG,GAAG,IAAI,IAAE,CAAC,CAAC,GAAG,IAAI,SAAE,MAAM,EAAE,MAAM,OAAO,CAAC,GAAG,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,SAAE,MAAM,EAAE,MAAM,MAAM,GAAG,IAAI,IAAE,IAAI,GAAG,IAAI,IAAE,KAAE,GAAG,IAAI,IAAE;AAAA,MACnK,WAAW;AAAA,MACX,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,cAAc;AAAA;AAAA,IAEhB,CAAC,GAAG,IAAI,IAAE;AAAA;AAAA,MAER,UAAU;AAAA,MACV,WAAW;AAAA,IACb,CAAC,GAAG,IAAI,SAAE,OAAO;AAAA,MACf,CAAC,0BAA0B,EAAE,aAAa,eAAe,UAAU,EAAE,GAAG;AAAA,MACxE,wBAAwB,EAAE,MAAM;AAAA,IAClC,EAAE,GAAG,IAAI,MAAM;AACb,eAAS,iBAAiB,aAAa,GAAG,EAAE,SAAS,MAAG,CAAC,GAAG,SAAS,iBAAiB,WAAW,CAAC,GAAG,kBAAkB,WAAW,SAAS,iBAAiB,aAAa,GAAG,EAAE,SAAS,MAAG,CAAC,GAAG,SAAS,iBAAiB,YAAY,CAAC;AAAA,IACvO,GAAG,IAAI,MAAM;AACX,eAAS,oBAAoB,aAAa,GAAG,EAAE,SAAS,MAAG,CAAC,GAAG,SAAS,oBAAoB,WAAW,CAAC,GAAG,kBAAkB,WAAW,SAAS,oBAAoB,aAAa,GAAG,EAAE,SAAS,MAAG,CAAC,GAAG,SAAS,oBAAoB,YAAY,CAAC;AAAA,IACnP,GAAG,IAAI,CAAC,GAAG,MAAM;AACf,YAAM,IAAI,EAAE,OAAO,QAAQ,uBAAuB;AAClD,UAAI,GAAG;AACL,cAAM,EAAE,MAAM,GAAG,KAAK,EAAE,IAAI,EAAE,sBAAsB,GAAG,EAAE,SAAS,GAAG,SAAS,EAAE,IAAI,kBAAkB,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,IAAI;AAC3I,UAAE,MAAM,eAAe,EAAE,aAAa,IAAI,IAAI,IAAI;AAAA,MACpD;AACA,QAAE,GAAG,EAAE,MAAM,YAAY,MAAI,EAAE,MAAM,iBAAiB;AAAA,IACxD,GAAG,IAAI,CAAC,MAAM;AACZ,QAAE,MAAM,cAAc,EAAE,eAAe,GAAG,EAAE,MAAM,WAAW,MAAI,sBAAsB,MAAM;AAC3F,UAAE,EAAE,CAAC,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,IAAE;AAAA,MACvC,CAAC;AAAA,IACH,GAAG,IAAI,CAAC,MAAM;AACZ,QAAE,MAAM,aAAa,OAAO,aAAa,EAAE,gBAAgB,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,IAAE,IAAI,EAAE,MAAM,YAAY,OAAI,EAAE,MAAM,iBAAiB,MAAM,WAAW,MAAM;AACrK,UAAE,MAAM,WAAW,OAAI,EAAE;AAAA,MAC3B,GAAG,GAAG;AAAA,IACR,GAAG,IAAI,CAAC,GAAG,MAAM;AACf,wBAAkB,WAAW,EAAE,eAAe,GAAG,EAAE,MAAM,aAAa,KAAK,aAAa,EAAE,MAAM,SAAS,GAAG,EAAE,MAAM,YAAY,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,MAAM,WAAW,SAAS,EAAE,MAAM,WAAW,GAAG,EAAE,MAAM,YAAY,WAAW,MAAM,EAAE,MAAM,WAAW,MAAM,GAAG,KAAK,EAAE,MAAM,YAAY,EAAE,kBAAkB,EAAE,OAAO,GAAG,OAAO,EAAE,GAAG,IAAE;AAAA,IAC3U,GAAG,IAAI,CAAC,GAAG,MAAM;AACf,UAAI,EAAE,qBAAqB,EAAE,OAAO,GAAG,OAAO,EAAE,GAAG,IAAE,GAAG,EAAE,eAAe;AACvE,YAAI,IAAI;AACR,UAAE,QAAQ,EAAE,MAAM,IAAI,CAAC,GAAG,OAAO,EAAE,OAAO,MAAM,IAAI,EAAE,MAAM,EAAE,KAAK,MAAM,MAAM,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,QAAQ,GAAG,EAAE,iBAAiB,EAAE,OAAO,GAAG,OAAO,GAAG,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,WAAW,EAAE,OAAO,GAAG,OAAO,EAAE,GAAG,IAAE;AAAA,MAC9N;AAAA,IACF,GAAG,IAAI,CAAC,GAAG,MAAM;AACf,QAAE,cAAc;AAAA,QACd,OAAO;AAAA,QACP,OAAO,EAAE,MAAM,CAAC,EAAE;AAAA,QAClB,MAAM,EAAE,MAAM,CAAC;AAAA,MACjB,CAAC;AAAA,IACH,GAAG,IAAI,CAAC,MAAM;AACZ,YAAM,IAAI,EAAE,MAAM,sBAAsB,GAAG,EAAE,SAAS,GAAG,SAAS,EAAE,IAAI,kBAAkB,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,IAAI;AAC/H,aAAO;AAAA,QACL,GAAG,KAAK,EAAE,aAAa,IAAI,EAAE,MAAM,gBAAgB,EAAE;AAAA,QACrD,GAAG,KAAK,EAAE,aAAa,EAAE,MAAM,eAAe,KAAK,EAAE;AAAA,MACvD;AAAA,IACF,GAAG,IAAI,CAAC,MAAM;AACZ,UAAI,EAAE,EAAE,aAAa,MAAM,GAAG;AAC9B,YAAM,IAAI,EAAE,MAAM,EAAE,aAAa,iBAAiB,aAAa;AAC/D,aAAO,EAAE,OAAO,CAAC,EAAE,eAAe,IAAI,IAAI,IAAI,IAAI,MAAM;AAAA,IAC1D,GAAG,IAAI,CAAC,MAAM;AACZ,YAAM,IAAI,EAAE,MAAM;AAClB,UAAI,IAAI;AAAA,QACN,eAAe,EAAE,CAAC;AAAA,QAClB,eAAe,EAAE,CAAC;AAAA,QAClB,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,MACvB;AACA,YAAM,IAAI,KAAK,EAAE,iBAAiB,IAAI,EAAE,gBAAgB,IAAI,OAAO,EAAE,iBAAiB,IAAI,EAAE,gBAAgB,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;AAC7I,UAAI,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,MAAM,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK;AACpE,YAAM,IAAI,EAAE,MAAM,OAAO,KAAK,EAAE,MAAM,EAAE,eAAe,KAAK,EAAE,MAAM,OAAO,KAAK,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC;AACtG,UAAI,KAAK,IAAI;AACX,aAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,KAAK,IAAI,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,eAAe,CAAC,MAAM,EAAE,OAAO,KAAK,IAAI,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE;AAC1K;AAAA,MACF;AACA,UAAI,EAAE,gBAAgB;AACpB,cAAM,IAAI,EAAE,GAAG,CAAC;AAChB,YAAI,CAAC,EAAG;AACR,SAAC,EAAE,MAAM,GAAG,eAAe,EAAE,IAAI,IAAI,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,MAAM,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK;AAAA,MACvF;AACA,YAAM,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,GAAG,GAAG,EAAE,GAAG,IAAI,MAAM,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,IAAI,MAAM,IAAI,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,GAAG,GAAG,EAAE,GAAG;AAAA,IACpN,GAAG,IAAI,CAAC,GAAG,MAAM;AACf,YAAM,IAAI,EAAE,MAAM,gBAAgB,IAAI,CAAC,GAAG,IAAI,CAAC;AAC/C,aAAO,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,sBAAsB,GAAG,EAAE,CAAC,IAAI,KAAK,EAAE,MAAM,QAAQ,CAAC,GAAG,MAAM;AACrI,YAAI,EAAE,CAAC,KAAK,KAAK,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,uBAAuB,EAAE;AAAA,MACpE,CAAC,GAAG,EAAE,gBAAgB,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,WAAW,EAAE,sBAAsB,GAAG,EAAE,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,MAAM,QAAQ,CAAC,GAAG,MAAM;AACxI,YAAI,KAAK,KAAK,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,uBAAuB,EAAE;AAAA,MACjE,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,OAAO,MAAM,EAAE,sBAAsB,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,gBAAgB,EAAE,eAAe,QAAQ,IAAI,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,sBAAsB,GAAG,EAAE,CAAC,IAAI,IAAI,KAAK,EAAE,MAAM,QAAQ,CAAC,GAAG,MAAM;AAC3P,YAAI,KAAK,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,uBAAuB,EAAE;AAAA,MACnE,CAAC,GAAG,EAAE,gBAAgB,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,WAAW,EAAE,sBAAsB,GAAG,EAAE,MAAM,QAAQ,CAAC,GAAG,MAAM;AAC1G,YAAI,EAAE,QAAQ,KAAK,KAAK,IAAI,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,uBAAuB,EAAE;AAAA,MAC/E,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,OAAO,MAAM,EAAE,gBAAgB,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,QAAQ,EAAE,MAAM,GAAG,eAAe,EAAE;AAAA,IACpG,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,OAAO,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,OAAO,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,IAAI,IAAI,EAAE,OAAO,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,QAAQ,KAAK,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI,KAAK,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC,GAAG,KAAK,MAAM;AACvT,UAAI;AACJ,YAAM,IAAI,MAAM,OAAO,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,aAAa,CAAC,CAAC;AACxE,iBAAW,KAAK,GAAG;AACjB,cAAM,IAAI,EAAE,UAAU,SAAS,kBAAkB,GAAG,IAAI,EAAE,UAAU,SAAS,sBAAsB;AACnG,SAAC,KAAK,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,KAAK,8GAA8G;AAAA,MACtJ;AAAA,IACF,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,UAAO;AACvB,YAAM,IAAI,IAAI,GAAG,IAAI,SAAS,cAAc,KAAK;AACjD,QAAE,UAAU,IAAI,sBAAsB,GAAG,MAAM,EAAE,cAAc,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,OAAO,SAAS,OAAO,kBAAkB,WAAW,EAAE,eAAe,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,WAAW,aAAa,GAAG,CAAC;AAAA,IACzQ,GAAG,KAAK,CAAC,MAAM;AACb,QAAE,cAAc,QAAQ,EAAE,UAAU,QAAQ,EAAE,aAAa,QAAQ,EAAE,OAAO;AAAA,IAC9E,GAAG,IAAI,MAAM;AACX,UAAI;AACJ,YAAM,IAAI,MAAM,OAAO,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,aAAa,CAAC,CAAC;AACxE,iBAAW,KAAK;AACd,UAAE,UAAU,SAAS,sBAAsB,KAAK,GAAG,CAAC;AACtD,UAAI,IAAI;AACR,iBAAW,KAAK;AACd,UAAE,UAAU,SAAS,kBAAkB,MAAM,CAAC,KAAK,EAAE,gBAAgB,EAAE,GAAG,GAAG,IAAE,IAAI,KAAK,EAAE,GAAG,CAAC,GAAG;AAAA,IACrG,GAAG,KAAK,CAAC,EAAE,KAAK,GAAG,GAAG,EAAE,MAAM;AAC5B,YAAM,IAAI,EAAE,MAAM,CAAC;AACnB,iBAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,CAAC,EAAG,GAAE,CAAC,IAAI;AAAA,IACjD,GAAG,KAAK,CAAC,MAAM;AACb,UAAI;AACJ,UAAI,IAAI;AACR,YAAM,OAAO,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,aAAa,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,EAAE,UAAU,SAAS,kBAAkB,KAAK,KAAK,EAAE,WAAW,EAAE,EAAE,EAAE,GAAG,EAAE,MAAM,OAAO,GAAG,GAAG,EAAE,GAAG,GAAG,OAAO,EAAE,CAAC,GAAG,EAAE,MAAM,QAAQ,CAAC,GAAG,MAAM,EAAE,QAAQ,CAAC,GAAG,EAAE,SAAS,SAAE,MAAM;AACvP,UAAE,GAAG,EAAE,EAAE,WAAW,EAAE,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC;AAAA,MACvE,CAAC;AAAA,IACH,GAAG,KAAK,CAAC,MAAM;AACb,YAAM,IAAI,EAAE,MAAM,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC;AAC7C,QAAE,MAAM,CAAC,EAAE,KAAK;AAChB,YAAM,IAAI,EAAE,MAAM,OAAO,GAAG,CAAC,EAAE,CAAC;AAChC,QAAE,MAAM,QAAQ,CAAC,GAAG,MAAM,EAAE,QAAQ,CAAC,GAAG,SAAE,MAAM;AAC9C,UAAE,GAAG,EAAE,eAAe,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,aAAa,EAAE,GAAG,EAAE,EAAE,CAAC;AAAA,MACjE,CAAC;AAAA,IACH,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM;AACjB,OAAC,EAAE,aAAa,CAAC,EAAE,cAAc,GAAG,IAAI,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,cAAc,QAAQ,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE,SAAS,EAAE,SAAS;AAAA,IAClJ,GAAG,KAAK,MAAM;AACZ,YAAM,IAAI,MAAM,EAAE;AAClB,UAAI,IAAI;AACR,YAAM,IAAI,CAAC,GAAG,IAAI,CAAC;AACnB,iBAAW,KAAK,EAAE;AAChB,UAAE,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;AAC5H,UAAI,OAAO,EAAE,GAAG,GAAG,CAAC;AAAA,IACtB,GAAG,KAAK,MAAM;AACZ,UAAI,IAAI;AACR,YAAM,IAAI,CAAC,GAAG,IAAI,CAAC;AACnB,UAAI,IAAI;AACR,iBAAW,KAAK,EAAE;AAChB,aAAK,EAAE,MAAM,EAAE,cAAc,QAAQ,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;AAC3G,UAAI,IAAI;AACR,UAAI,IAAI,KAAK;AACX,mBAAW,KAAK,EAAE;AAChB,YAAE,cAAc,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE,QAAQ,IAAI,EAAE,GAAG,GAAG,EAAE,GAAG,IAAI,KAAK,EAAE;AACjG,YAAI,OAAO,EAAE,GAAG,GAAG,CAAC;AAAA,MACtB;AAAA,IACF,GAAG,KAAK,CAAC,EAAE,WAAW,GAAG,aAAa,EAAE,IAAI,CAAC,MAAM;AACjD,UAAI,IAAI,MAAM,EAAE,OAAO,IAAI;AAC3B,YAAM,IAAI,CAAC,GAAG,IAAI,CAAC;AACnB,QAAE,KAAK,OAAO,SAAS,EAAE,cAAc,UAAU,SAAS,KAAK,MAAM,EAAE,cAAc,EAAE,QAAQ;AAC/F,iBAAW,KAAK,EAAE;AAChB,aAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;AAC9E,UAAI,EAAE,KAAK,IAAI,CAAC,IAAI,MAAM;AACxB,mBAAW,KAAK,EAAE;AAChB,WAAC,KAAK,OAAO,SAAS,EAAE,eAAe,SAAS,KAAK,OAAO,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,IAAI,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;AACrN,YAAI,OAAO,EAAE,GAAG,GAAG,CAAC;AAAA,MACtB;AAAA,IACF,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM;AAClB,UAAI;AACJ,UAAI,IAAI,IAAI,KAAK,EAAE,QAAQ,EAAE,UAAU,IAAI,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC,GAAG,MAAM;AAC7F,YAAI,IAAI,KAAK,CAAC,EAAE,SAAS,EAAE,EAAE,GAAG;AAC9B,gBAAM,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,OAAO,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,IAAI,EAAE;AAClE,eAAK,GAAG,EAAE,OAAO;AAAA,QACnB,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,GAAG;AAC5B,gBAAM,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,OAAO,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,IAAI,EAAE;AAClE,eAAK,GAAG,EAAE,OAAO;AAAA,QACnB;AAAA,MACF,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,OAAO,SAAE,MAAM;AAC/B,UAAE,SAAS,QAAQ,KAAK,wEAAwE;AAAA,MAClG,CAAC;AAAA,IACH,GAAG,IAAI,CAAC,GAAG,IAAI,QAAQ,IAAI,UAAO;AAChC,YAAM,KAAK,KAAK,OAAO,SAAS,EAAE,UAAU,EAAE,MAAM,kBAAkB;AACtE,QAAE,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG,MAAM,QAAQ,EAAE,OAAO,EAAE;AAAA,QAC5B,GAAG,KAAK,MAAM,QAAQ;AAAA,UACpB,UAAU,EAAE,MAAM,KAAK,EAAE,gBAAgB,IAAI,EAAE;AAAA,UAC/C,UAAU,EAAE,MAAM,KAAK,EAAE,gBAAgB,IAAI,EAAE;AAAA,QACjD;AAAA,QACA,OAAO,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,EAAE;AAAA,MACtE,CAAC;AAAA,IACH;AACA,UAAE,MAAM,EAAE,eAAe,MAAM,EAAE,CAAC,GAAG,UAAE,MAAM;AAC3C,SAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,GAAG,EAAE,QAAQ;AAAA,IACxC,CAAC,GAAG,gBAAE,MAAM,EAAE,QAAQ,KAAE;AACxB,UAAM,KAAK,MAAM;AACf,UAAI;AACJ,aAAO;AAAA,QACL;AAAA,QACA,EAAE,KAAK,GAAG,OAAO,EAAE,MAAM;AAAA,SACxB,IAAI,EAAE,YAAY,OAAO,SAAS,EAAE,KAAK,CAAC;AAAA,MAC7C;AAAA,IACF;AACA,WAAO,QAAE,SAAS,CAAC,GAAG,QAAE,gBAAgB,CAAC,GAAG,QAAE,cAAc,SAAE,MAAM,EAAE,UAAU,CAAC,GAAG,QAAE,iBAAiB,EAAE,GAAG,QAAE,aAAa,EAAE,GAAG,QAAE,gBAAgB,EAAE,GAAG,QAAE,eAAe,CAAC,GAAG,CAAC,GAAG,OAAO,UAAE,GAAG,YAAG,wBAAG,EAAE,CAAC;AAAA,EACvM;AACF;AAxNA,IAwNG,KAAK;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,MAAM,EAAE,MAAM,CAAC,QAAQ,MAAM,EAAE;AAAA,IAC/B,SAAS,EAAE,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,EAAE;AAAA,IAC9C,SAAS,EAAE,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,IAAI;AAAA,EAClD;AAAA,EACA,MAAM,GAAG;AACP,QAAI;AACJ,UAAMA,KAAI,GAAG,IAAI,OAAE,eAAe,GAAG,IAAI,OAAE,WAAW,GAAG,IAAI,OAAE,YAAY,GAAG,IAAI,OAAE,cAAc,GAAG,IAAI,OAAE,aAAa,GAAG,KAAK,IAAI,mBAAG,MAAM,OAAO,SAAS,EAAE,KAAK,IAAI,OAAE,cAAc,GAAG,IAAI,SAAE,MAAM,EAAE,MAAM,CAAC,CAAC,GAAG,IAAI,IAAE,IAAI,GAAG,IAAI,SAAE,MAAM;AAC3O,YAAM,IAAI,MAAMA,GAAE,IAAI,KAAKA,GAAE,SAAS,SAAS,IAAI,WAAWA,GAAE,IAAI;AACpE,aAAO,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,KAAK,GAAG,EAAE,KAAK;AAAA,IAC/C,CAAC,GAAG,IAAI,SAAE,MAAM;AACd,YAAM,IAAI,WAAWA,GAAE,OAAO;AAC9B,aAAO,MAAM,CAAC,IAAI,IAAI;AAAA,IACxB,CAAC,GAAG,IAAI,SAAE,MAAM;AACd,YAAM,IAAI,WAAWA,GAAE,OAAO;AAC9B,aAAO,MAAM,CAAC,IAAI,MAAM;AAAA,IAC1B,CAAC,GAAG,IAAI,SAAE,MAAM;AACd,UAAI;AACJ,aAAO,GAAG,EAAE,QAAQ,WAAW,OAAO,MAAM,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,IAAI;AAAA,IACpF,CAAC;AACD,WAAO,MAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,EAAE,KAAK,GAAG,MAAM,EAAE,CAAC,CAAC,GAAG,MAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,EAAE,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,MAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,EAAE,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,UAAE,MAAM;AAC9J,QAAE;AAAA,QACA,IAAI;AAAA,QACJ,IAAI,EAAE;AAAA,QACN,KAAK,EAAE;AAAA,QACP,KAAK,EAAE;AAAA;AAAA,QAEP,WAAWA,GAAE,SAAS,SAAS,OAAO,EAAE;AAAA,QACxC,MAAM,EAAE;AAAA;AAAA,MAEV,CAAC;AAAA,IACH,CAAC,GAAG,gBAAE,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAG,OAAO;AAAA,MAC3C,SAAS;AAAA,MACT,KAAK;AAAA,MACL,OAAO;AAAA,MACP,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,MAAG,CAAC,EAAE,GAAG,EAAE,EAAE,GAAG;AAAA,MAChD,OAAO,eAAG,EAAE,KAAK;AAAA,IACnB,GAAG;AAAA,MACD,WAAG,EAAE,QAAQ,SAAS;AAAA,IACxB,GAAG,CAAC;AAAA,EACN;AACF;", "names": ["h"]}
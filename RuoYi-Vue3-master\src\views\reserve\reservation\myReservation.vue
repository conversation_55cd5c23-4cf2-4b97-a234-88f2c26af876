<template>
  <div class="app-container my-reservation">
    <!-- 搜索表单区域 -->
    <div class="search-section" v-show="showSearch">
      <el-card class="search-card">
        <template #header>
          <div class="search-header">
            <span><el-icon><Search /></el-icon> 筛选条件</span>
          </div>
        </template>
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px" class="search-form">
          <el-form-item label="会议室" prop="roomId">
            <el-select
              v-model="queryParams.roomId"
              placeholder="请选择会议室"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="room in availableRooms"
                :key="room.roomId"
                :label="room.roomName"
                :value="room.roomId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 150px">
              <el-option
                v-for="dict in statusOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              style="width: 300px"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar-section">
      <el-card class="toolbar-card">
        <el-row :gutter="10" class="toolbar-row">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="Plus"
              @click="handleAdd"
              v-hasPermi="['reserve:reservation:add']"
              class="action-btn"
            >
              <span>新增预约</span>
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="info"
              icon="Search"
              @click="toggleSearch"
              class="action-btn"
            >
              {{ showSearch ? '隐藏搜索' : '显示搜索' }}
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="Refresh"
              @click="getList"
              class="action-btn"
            >
              刷新数据
            </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </el-card>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <el-card class="table-card">
        <template #header>
          <div class="table-header">
            <span><el-icon><List /></el-icon> 预约列表</span>
            <div class="table-stats">
              <el-tag type="info" size="small">共 {{ total }} 条记录</el-tag>
            </div>
          </div>
        </template>

        <!-- 空状态 -->
        <div v-if="!loading && reservationList.length === 0" class="empty-state">
          <el-empty description="暂无预约记录">
            <el-button type="primary" @click="handleAdd">立即预约</el-button>
          </el-empty>
        </div>

        <!-- 数据表格 -->
        <el-table
          v-else
          v-loading="loading"
          :data="reservationList"
          class="reservation-table"
          stripe
          border
          :header-cell-style="{ background: '#f8fafc', color: '#606266', fontWeight: '600' }"
        >
          <el-table-column label="会议室" align="center" prop="roomName" min-width="120">
            <template #default="scope">
              <div class="room-info">
                <el-icon class="room-icon"><OfficeBuilding /></el-icon>
                <span class="room-name">{{ getRoomName(scope.row.roomId) }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="会议主题" align="center" prop="meetingTitle" min-width="150">
            <template #default="scope">
              <div class="meeting-title">
                <el-icon><Document /></el-icon>
                <span>{{ scope.row.meetingTitle }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="开始时间" align="center" prop="startTime" width="180">
            <template #default="scope">
              <div class="time-info">
                <el-icon class="time-icon"><Clock /></el-icon>
                <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="结束时间" align="center" prop="endTime" width="180">
            <template #default="scope">
              <div class="time-info">
                <el-icon class="time-icon"><Clock /></el-icon>
                <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" align="center" prop="status" width="100">
            <template #default="scope">
              <el-tag
                :type="statusTagType(scope.row.status)"
                class="status-tag"
                effect="dark"
              >
                {{ formatStatus(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
            <template #default="scope">
              <div class="action-buttons">
                <el-button
                  type="primary"
                  size="small"
                  icon="Edit"
                  @click="handleUpdate(scope.row)"
                  :disabled="!canUpdate(scope.row)"
                  v-hasPermi="['reserve:reservation:edit']"
                  class="action-btn-small"
                >修改</el-button>
                <el-button
                  type="danger"
                  size="small"
                  icon="Delete"
                  @click="handleCancel(scope.row)"
                  :disabled="!canCancel(scope.row)"
                  v-hasPermi="['reserve:reservation:cancel']"
                  class="action-btn-small"
                >取消</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-wrapper" v-show="total > 0">
          <pagination
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </el-card>
    </div>

    <!-- 添加或修改会议室预约申请对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="600px"
      append-to-body
      class="reservation-dialog"
      :close-on-click-modal="false"
    >
      <template #header>
        <div class="dialog-header">
          <el-icon><EditPen /></el-icon>
          <span>{{ title }}</span>
        </div>
      </template>

      <el-form ref="reservationRef" :model="form" :rules="rules" label-width="100px" class="reservation-form">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="会议室" prop="roomId">
              <el-select v-model="form.roomId" placeholder="请选择会议室" clearable style="width: 100%">
                <el-option
                  v-for="room in availableRooms"
                  :key="room.roomId"
                  :label="room.roomName"
                  :value="room.roomId"
                >
                  <div class="room-option">
                    <span class="room-name">{{ room.roomName }}</span>
                    <span class="room-capacity">容量: {{ room.capacity }}人</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请人ID" prop="userId">
              <el-input v-model="form.userId" placeholder="请输入申请人ID" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请人姓名" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入申请人姓名" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="部门ID" prop="deptId">
              <el-input v-model="form.deptId" placeholder="请输入部门ID" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门名称" prop="deptName">
              <el-input v-model="form.deptName" placeholder="请输入部门名称" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="会议主题" prop="meetingTitle">
              <el-input
                v-model="form.meetingTitle"
                placeholder="请输入会议主题"
                :prefix-icon="Document"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                clearable
                v-model="form.startTime"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledDate"
                placeholder="请选择会议开始时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                clearable
                v-model="form.endTime"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledDate"
                placeholder="请选择会议结束时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="参会人员" prop="attendees">
              <el-input
                v-model="form.attendees"
                type="textarea"
                placeholder="请输入参会人员，多人请用逗号分隔"
                :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                placeholder="请输入备注信息（可选）"
                :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel" size="large">取 消</el-button>
          <el-button type="primary" @click="submitForm" size="large">
            <el-icon><Check /></el-icon>
            确 定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MyReservation">
import { ref, reactive, toRefs, computed } from 'vue'
import { myReservationList, cancelReservation, addReservation, updateReservation, getReservation } from "@/api/reserve/reservation"
import { getUserProfile } from "@/api/system/user"
import { listRoom } from "@/api/search/room"
import { getCurrentInstance } from 'vue'
import useUserStore from '@/store/modules/user'
import {
  User,
  Search,
  List,
  OfficeBuilding,
  Document,
  Clock,
  EditPen,
  Check
} from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance()
const userStore = useUserStore()

// 状态选项
const statusOptions = [
  { dictLabel: '已取消', dictValue: '0' },
  { dictLabel: '待审核', dictValue: '1' },
  { dictLabel: '已通过', dictValue: '2' },
  { dictLabel: '已拒绝', dictValue: '3' },
  { dictLabel: '已完成', dictValue: '4' }
]

// 数据定义
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)
const dateRange = ref([])
const reservationList = ref([])
const open = ref(false)
const title = ref("")
const userProfile = ref({})
const roomList = ref([])

//仅显示 status 为 1 的会议室
const availableRooms = computed(() => {
  return roomList.value.filter(room => room.status === 1)
})

// 表单数据
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    roomId: null,
    status: null,
    // 会自动添加当前用户ID
  },
  rules: {
    roomId: [{ required: true, message: "会议室ID不能为空", trigger: "blur" }],
    userId: [{ required: true, message: "申请人ID不能为空", trigger: "blur" }],
    userName: [{ required: true, message: "申请人姓名不能为空", trigger: "blur" }],
    deptId: [{ required: true, message: "部门ID不能为空", trigger: "blur" }],
    deptName: [{ required: true, message: "部门名称不能为空", trigger: "blur" }],
    meetingTitle: [{ required: true, message: "会议主题不能为空", trigger: "blur" }],
    startTime: [{ required: true, message: "会议开始时间不能为空", trigger: "blur" }],
    endTime: [{ required: true, message: "会议结束时间不能为空", trigger: "blur" }],
  }
})

const { form, queryParams, rules } = toRefs(data)

/** 查询我的预约列表 */
function getList() {
  loading.value = true
  // 自动添加当前用户ID
  queryParams.value.userId = userStore.id
  myReservationList(queryParams.value).then(response => {
    reservationList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  
  // 👇 添加这段日期处理代码
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.value.startTime = dateRange.value[0] + ' 00:00:00'  
    queryParams.value.endTime = dateRange.value[1] + ' 23:59:59'   
  } else {
    queryParams.value.startTime = null
    queryParams.value.endTime = null
  }
  
  getList()
}


/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 状态格式化 */
function formatStatus(status) {
  const map = { 0: '已取消', 1: '待审核', 2: '已通过', 3: '已拒绝', 4: '已完成' }
  return map[status] || '未知'
}

/** 状态标签类型 */
function statusTagType(status) {
  const map = { 0: 'info', 1: 'warning', 2: 'success', 3: 'danger', 4: '' }
  return map[status]
}

/** 表单重置 */
function reset() {
  form.value = {
    reservationId: null,
    roomId: null,
    userId: null,
    userName: null,
    deptId: null,
    deptName: null,
    meetingTitle: null,
    startTime: null,
    endTime: null,
    attendees: null,
    status: null,
    remark: null,
    createTime: null,
    updateTime: null
  }
  proxy.resetForm("reservationRef")
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 获取用户详细信息 */
function getUserInfo() {
  getUserProfile().then(response => {
    userProfile.value = response.data
  })
}

/** 获取会议室列表 */
function getRoomList() {
  listRoom({pageNum: 1, pageSize: 1000}).then(response => {
    roomList.value = response.rows || []
  })
}

/** 根据会议室ID获取会议室名称 */
function getRoomName(roomId) {
  const room = roomList.value.find(r => r.roomId == roomId)
  return room ? room.roomName : `会议室${roomId}`
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  // 自动填充当前用户信息
  form.value.userId = userStore.id
  form.value.userName = userStore.name
  // 从用户详细信息中获取部门信息
  if (userProfile.value.dept) {
    form.value.deptId = userProfile.value.dept.deptId
    form.value.deptName = userProfile.value.dept.deptName
  }
  open.value = true
  title.value = "添加会议室预约申请"
}

/** 修改预约 */
function handleUpdate(row) {
  reset()
  const _reservationId = row.reservationId
  getReservation(_reservationId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改会议室预约申请"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["reservationRef"].validate(valid => {
    if (valid) {
      if (form.value.reservationId != null) {
        updateReservation(form.value).then(() => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addReservation(form.value).then(() => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 切换搜索显示 */
function toggleSearch() {
  showSearch.value = !showSearch.value
}

/** 禁用过去的日期 */
function disabledDate(time) {
  return time.getTime() < Date.now() - 8.64e7 // 禁用今天之前的日期
}

/** 取消预约 */
function handleCancel(row) {
  proxy.$modal.confirm('确认要取消该预约吗?', "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    return cancelReservation(row.reservationId)
  }).then(() => {
    proxy.$modal.msgSuccess("取消成功")
    getList()
  }).catch(() => {})
}

/** 判断是否可以修改 */
function canUpdate(row) {
  return row.status == 1 // 只有待审核状态可以修改
}

/** 判断是否可以取消 */
function canCancel(row) {
  return row.status != 0 // 除了已取消状态，其他都可以取消
}

// 初始化加载
getUserInfo()
getRoomList()
getList()
</script>

<style scoped lang="scss">
.my-reservation {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

/* 页面标题区域 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 32px 40px;
  margin-bottom: 24px;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  pointer-events: none;
}

.header-content {
  position: relative;
  z-index: 1;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 12px 0;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

  .el-icon {
    margin-right: 12px;
    font-size: 32px;
  }
}

.page-description {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 20px;
}

.search-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
}

.search-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #303133;

  .el-icon {
    margin-right: 8px;
    color: #409eff;
  }
}

.search-form {
  padding: 8px 0;

  .el-form-item {
    margin-bottom: 16px;
  }
}

/* 工具栏区域 */
.toolbar-section {
  margin-bottom: 20px;
}

.toolbar-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
}

.toolbar-row {
  align-items: center;
}

.action-btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 10px 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  span {
    margin-left: 4px;
  }
}

/* 表格区域 */
.table-section {
  margin-bottom: 20px;
}

.table-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;

  .el-icon {
    margin-right: 8px;
    color: #409eff;
  }
}

.table-stats {
  display: flex;
  gap: 8px;
}

/* 空状态 */
.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* 表格样式 */
.reservation-table {
  border-radius: 8px;
  overflow: hidden;

  :deep(.el-table__header) {
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  }

  :deep(.el-table__row) {
    transition: all 0.3s ease;

    &:hover {
      background-color: #f0f9ff !important;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
  }

  :deep(.el-table__cell) {
    border-color: #f1f5f9;
    padding: 16px 12px;
  }
}

.room-info {
  display: flex;
  align-items: center;
  justify-content: center;

  .room-icon {
    margin-right: 8px;
    color: #409eff;
    font-size: 16px;
  }

  .room-name {
    font-weight: 500;
    color: #303133;
  }
}

.meeting-title {
  display: flex;
  align-items: center;
  justify-content: center;

  .el-icon {
    margin-right: 8px;
    color: #67c23a;
    font-size: 16px;
  }

  span {
    font-weight: 500;
    color: #303133;
  }
}

.time-info {
  display: flex;
  align-items: center;
  justify-content: center;

  .time-icon {
    margin-right: 8px;
    color: #e6a23c;
    font-size: 16px;
  }

  span {
    font-weight: 500;
    color: #606266;
    font-size: 13px;
  }
}

.status-tag {
  font-weight: 600;
  border-radius: 6px;
  padding: 4px 12px;
}

.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: center;
  flex-wrap: nowrap; /* 强制一行显示 */
}

.action-btn-small {
  padding: 6px 10px;
  font-size: 12px;
  border-radius: 4px;
  font-weight: 400;
  border: 1px solid transparent;
  transition: all 0.2s ease;
  min-width: 52px;
  flex-shrink: 0; /* 防止按钮压缩 */
  
  /* 简约配色 */
  &.el-button--primary {
    background: #409eff;
    border-color: #409eff;
    color: white;
    
    &:hover:not(:disabled) {
      background: #66b1ff;
      border-color: #66b1ff;
    }
    
    &:disabled {
      background: #c0c4cc;
      border-color: #c0c4cc;
      color: #ffffff;
      cursor: not-allowed;
    }
  }
  
  &.el-button--success {
    background: #67c23a;
    border-color: #67c23a;
    color: white;
    
    &:hover:not(:disabled) {
      background: #85ce61;
      border-color: #85ce61;
    }
    
    &:disabled {
      background: #c0c4cc;
      border-color: #c0c4cc;
      color: #ffffff;
      cursor: not-allowed;
    }
  }
  
  &.el-button--warning {
    background: #e6a23c;
    border-color: #e6a23c;
    color: white;
    
    &:hover:not(:disabled) {
      background: #ebb563;
      border-color: #ebb563;
    }
    
    &:disabled {
      background: #c0c4cc;
      border-color: #c0c4cc;
      color: #ffffff;
      cursor: not-allowed;
    }
  }
  
  &.el-button--danger {
    background: #f56c6c;
    border-color: #f56c6c;
    color: white;
    
    &:hover:not(:disabled) {
      background: #f78989;
      border-color: #f78989;
    }
    
    &:disabled {
      background: #c0c4cc;
      border-color: #c0c4cc;
      color: #ffffff;
      cursor: not-allowed;
    }
  }
  
  &.el-button--info {
    background: #909399;
    border-color: #909399;
    color: white;
    
    &:hover:not(:disabled) {
      background: #a6a9ad;
      border-color: #a6a9ad;
    }
    
    &:disabled {
      background: #c0c4cc;
      border-color: #c0c4cc;
      color: #ffffff;
      cursor: not-allowed;
    }
  }
  
  /* 移除阴影和变换效果，保持简约 */
  &:hover:not(:disabled) {
    transform: none;
    box-shadow: none;
  }
  
  /* 图标样式 */
  .el-icon {
    margin-right: 4px;
    font-size: 12px;
  }
}

/* 分页样式 */
.pagination-wrapper {
  margin-top: 24px;
  padding: 16px 0;
  border-top: 1px solid #f1f5f9;
  display: flex;
  justify-content: center;
}

/* 对话框样式 */
.reservation-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    margin: 0;
  }

  :deep(.el-dialog__body) {
    padding: 32px 24px;
    background: #fafbfc;
  }

  :deep(.el-dialog__footer) {
    background: white;
    padding: 20px 24px;
    border-top: 1px solid #f1f5f9;
  }
}

.dialog-header {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;

  .el-icon {
    margin-right: 12px;
    font-size: 20px;
  }
}

.reservation-form {
  .el-form-item {
    margin-bottom: 20px;

    :deep(.el-form-item__label) {
      font-weight: 600;
      color: #303133;
    }

    :deep(.el-input__wrapper) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.is-focus {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }

    :deep(.el-textarea__inner) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &:focus {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }

    :deep(.el-date-editor) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.is-focus {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }
  }
}

.room-option {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .room-name {
    font-weight: 500;
    color: #303133;
  }

  .room-capacity {
    font-size: 12px;
    color: #909399;
    background: #f5f7fa;
    padding: 2px 8px;
    border-radius: 4px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .el-button {
    border-radius: 8px;
    font-weight: 500;
    padding: 12px 24px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .my-reservation {
    padding: 16px;
  }

  .page-header {
    padding: 24px 32px;
  }

  .page-title {
    font-size: 24px;

    .el-icon {
      font-size: 28px;
    }
  }
}

@media (max-width: 768px) {
  .my-reservation {
    padding: 12px;
  }

  .page-header {
    padding: 20px 24px;
  }

  .page-title {
    font-size: 20px;
    flex-direction: column;
    text-align: center;

    .el-icon {
      font-size: 24px;
      margin-right: 0;
      margin-bottom: 8px;
    }
  }

  .page-description {
    font-size: 14px;
    text-align: center;
  }

  .search-form {
    :deep(.el-form-item) {
      display: block;
      margin-right: 0;
      margin-bottom: 16px;

      .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }

  .toolbar-row {
    flex-direction: column;
    gap: 12px;

    .el-col {
      width: 100%;
    }
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;

    .action-btn-small {
      width: 100%;
    }
  }

  .reservation-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 5vh auto;
    }

    :deep(.el-dialog__body) {
      padding: 20px 16px;
    }
  }

  .reservation-form {
    .el-row {
      .el-col {
        width: 100%;
      }
    }
  }

  .dialog-footer {
    flex-direction: column;

    .el-button {
      width: 100%;
      margin: 0;
    }
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-section,
.toolbar-section,
.table-section {
  animation: fadeInUp 0.6s ease-out;
}

.search-section {
  animation-delay: 0.1s;
}

.toolbar-section {
  animation-delay: 0.2s;
}

.table-section {
  animation-delay: 0.3s;
}

/* 自定义滚动条 */
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f1f5f9;
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
}
</style>
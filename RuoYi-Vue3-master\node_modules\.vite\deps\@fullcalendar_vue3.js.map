{"version": 3, "sources": ["../../@fullcalendar/vue3/src/options.ts", "../../@fullcalendar/vue3/src/FullCalendar.ts", "../../@fullcalendar/vue3/src/index.ts"], "sourcesContent": ["\nexport const OPTION_IS_COMPLEX: { [name: string]: boolean } = {\n  headerToolbar: true,\n  footerToolbar: true,\n  events: true,\n  eventSources: true,\n  resources: true\n}\n", "import { PropType, defineComponent, h, Fragment, Teleport, VNode } from 'vue'\nimport { Calendar, CalendarOptions } from '@fullcalendar/core'\nimport { CustomRenderingStore, CustomRendering } from '@fullcalendar/core/internal'\nimport { OPTION_IS_COMPLEX } from './options.js'\n\nconst FullCalendar = defineComponent({\n  props: {\n    options: Object as PropType<CalendarOptions>\n  },\n\n  data() {\n    return {\n      renderId: 0,\n      customRenderingMap: new Map<string, CustomRendering<any>>()\n    }\n  },\n\n  methods: {\n    getApi(): Calendar {\n      return getSecret(this).calendar\n    },\n\n    buildOptions(suppliedOptions: CalendarOptions | undefined): CalendarOptions {\n      return {\n        ...suppliedOptions,\n        customRenderingMetaMap: kebabToCamelKeys(this.$slots),\n        handleCustomRendering: getSecret(this).handleCustomRendering,\n      }\n    },\n  },\n\n  render() {\n    const customRenderingNodes: VNode[] = []\n\n    for (const customRendering of this.customRenderingMap.values()) {\n      customRenderingNodes.push(\n        h(CustomRenderingComponent, {\n          key: customRendering.id,\n          customRendering,\n        })\n      )\n    }\n\n    return h('div', {\n      // when renderId is changed, Vue will trigger a real-DOM async rerender, calling beforeUpdate/updated\n      attrs: { 'data-fc-render-id': this.renderId }\n    }, h(Fragment, customRenderingNodes)) // for containing CustomRendering keys\n  },\n\n  mounted() {\n    const customRenderingStore = new CustomRenderingStore<any>()\n    getSecret(this).handleCustomRendering = customRenderingStore.handle.bind(customRenderingStore)\n\n    const calendarOptions = this.buildOptions(this.options)\n    const calendar = new Calendar(this.$el as HTMLElement, calendarOptions)\n    getSecret(this).calendar = calendar\n\n    calendar.render()\n    customRenderingStore.subscribe((customRenderingMap) => {\n      this.customRenderingMap = customRenderingMap // likely same reference, so won't rerender\n      this.renderId++ // force rerender\n      getSecret(this).needCustomRenderingResize = true\n    })\n  },\n\n  beforeUpdate() {\n    this.getApi().resumeRendering() // the watcher handlers paused it\n  },\n\n  updated() {\n    if (getSecret(this).needCustomRenderingResize) {\n      getSecret(this).needCustomRenderingResize = false\n      this.getApi().updateSize()\n    }\n  },\n\n  beforeUnmount() {\n    this.getApi().destroy()\n  },\n\n  watch: buildWatchers()\n})\n\nexport default FullCalendar\n\n// Custom Rendering\n// -------------------------------------------------------------------------------------------------\n\nconst CustomRenderingComponent = defineComponent({\n  props: {\n    customRendering: Object as PropType<CustomRendering<any>>\n  },\n\n  render() {\n    const customRendering = this.customRendering!\n    const innerContent = typeof customRendering.generatorMeta === 'function' ?\n      customRendering.generatorMeta(customRendering.renderProps) : // vue-normalized slot function\n      customRendering.generatorMeta // probably a vue JSX node returned from content-inject func\n\n    return h(Teleport, { to: customRendering.containerEl }, innerContent)\n  }\n})\n\n// Internals\n// -------------------------------------------------------------------------------------------------\n\ntype FullCalendarInstance = InstanceType<typeof FullCalendar>\n\ninterface FullCalendarSecret {\n  calendar: Calendar\n  handleCustomRendering: (customRendering: CustomRendering<any>) => void\n  needCustomRenderingResize?: boolean\n}\n\n// storing internal state:\n// https://github.com/vuejs/vue/issues/1988#issuecomment-163013818\nfunction getSecret(inst: any): FullCalendarSecret {\n  return inst as FullCalendarSecret\n}\n\nfunction buildWatchers() {\n\n  let watchers: { [member: string]: any } = {\n\n    // watches changes of ALL options and their nested objects,\n    // but this is only a means to be notified of top-level non-complex options changes.\n    options: {\n      deep: true,\n      handler(this: FullCalendarInstance, options: CalendarOptions) {\n        let calendar = this.getApi()\n        calendar.pauseRendering()\n\n        let calendarOptions = this.buildOptions(options)\n        calendar.resetOptions(calendarOptions)\n\n        this.renderId++ // will queue a rerender\n      }\n    }\n  }\n\n  for (let complexOptionName in OPTION_IS_COMPLEX) {\n\n    // handlers called when nested objects change\n    watchers[`options.${complexOptionName}`] = {\n      deep: true,\n      handler(this: FullCalendarInstance, val: any) {\n\n        // unfortunately the handler is called with undefined if new props were set, but the complex one wasn't ever set\n        if (val !== undefined) {\n\n          let calendar = this.getApi()\n          calendar.pauseRendering()\n          calendar.resetOptions({\n            [complexOptionName]: val\n          }, [complexOptionName])\n\n          this.renderId++ // will queue a rerender\n        }\n      }\n    }\n  }\n\n  return watchers\n}\n\n// General Utils\n// -------------------------------------------------------------------------------------------------\n\nfunction kebabToCamelKeys<V>(map: { [key: string]: V }): { [key: string]: V } {\n  const newMap: { [key: string]: V } = {}\n\n  for (const key in map) {\n    newMap[kebabToCamel(key)] = map[key]\n  }\n\n  return newMap\n}\n\nfunction kebabToCamel(s: string): string {\n  return s\n    .split('-')\n    .map((word, index) => index ? capitalize(word) : word)\n    .join('')\n}\n\nfunction capitalize(s: string): string {\n  return s.charAt(0).toUpperCase() + s.slice(1)\n}\n", "import FullCalendarComponent from './FullCalendar.js'\n\nexport default FullCalendarComponent\n"], "mappings": ";;;;;;;;;;;;;;AACO,IAAM,oBAAiD;EAC5D,eAAe;EACf,eAAe;EACf,QAAQ;EACR,cAAc;EACd,WAAW;;;;ACDb,IAAM,eAAe,gBAAgB;EACnC,OAAO;IACL,SAAS;;EAGX,OAAI;AACF,WAAO;MACL,UAAU;MACV,oBAAoB,oBAAI,IAAG;;EAE/B;EAEA,SAAS;IACP,SAAM;AACJ,aAAO,UAAU,IAAI,EAAE;IACzB;IAEA,aAAa,iBAA4C;AACvD,aAAO;QACL,GAAG;QACH,wBAAwB,iBAAiB,KAAK,MAAM;QACpD,uBAAuB,UAAU,IAAI,EAAE;;IAE3C;;EAGF,SAAM;AACJ,UAAM,uBAAgC,CAAA;AAEtC,eAAW,mBAAmB,KAAK,mBAAmB,OAAM,GAAI;AAC9D,2BAAqB,KACnB,EAAE,0BAA0B;QAC1B,KAAK,gBAAgB;QACrB;OACD,CAAC;;AAIN,WAAO,EAAE,OAAO;;MAEd,OAAO,EAAE,qBAAqB,KAAK,SAAQ;OAC1C,EAAE,UAAU,oBAAoB,CAAC;EACtC;EAEA,UAAO;AACL,UAAM,uBAAuB,IAAI,qBAAoB;AACrD,cAAU,IAAI,EAAE,wBAAwB,qBAAqB,OAAO,KAAK,oBAAoB;AAE7F,UAAM,kBAAkB,KAAK,aAAa,KAAK,OAAO;AACtD,UAAM,WAAW,IAAI,SAAS,KAAK,KAAoB,eAAe;AACtE,cAAU,IAAI,EAAE,WAAW;AAE3B,aAAS,OAAM;AACf,yBAAqB,UAAU,CAAC,uBAAsB;AACpD,WAAK,qBAAqB;AAC1B,WAAK;AACL,gBAAU,IAAI,EAAE,4BAA4B;IAC9C,CAAC;EACH;EAEA,eAAY;AACV,SAAK,OAAM,EAAG,gBAAe;EAC/B;EAEA,UAAO;AACL,QAAI,UAAU,IAAI,EAAE,2BAA2B;AAC7C,gBAAU,IAAI,EAAE,4BAA4B;AAC5C,WAAK,OAAM,EAAG,WAAU;;EAE5B;EAEA,gBAAa;AACX,SAAK,OAAM,EAAG,QAAO;EACvB;EAEA,OAAO,cAAa;CACrB;AAED,IAAA,uBAAe;AAKf,IAAM,2BAA2B,gBAAgB;EAC/C,OAAO;IACL,iBAAiB;;EAGnB,SAAM;AACJ,UAAM,kBAAkB,KAAK;AAC7B,UAAM,eAAe,OAAO,gBAAgB,kBAAkB,aAC5D,gBAAgB,cAAc,gBAAgB,WAAW;;MACzD,gBAAgB;;AAElB,WAAO,EAAE,UAAU,EAAE,IAAI,gBAAgB,YAAW,GAAI,YAAY;EACtE;CACD;AAeD,SAAS,UAAU,MAAS;AAC1B,SAAO;AACT;AAEA,SAAS,gBAAa;AAEpB,MAAI,WAAsC;;;IAIxC,SAAS;MACP,MAAM;MACN,QAAoC,SAAwB;AAC1D,YAAI,WAAW,KAAK,OAAM;AAC1B,iBAAS,eAAc;AAEvB,YAAI,kBAAkB,KAAK,aAAa,OAAO;AAC/C,iBAAS,aAAa,eAAe;AAErC,aAAK;MACP;;;AAIJ,WAAS,qBAAqB,mBAAmB;AAG/C,aAAS,WAAW,iBAAiB,EAAE,IAAI;MACzC,MAAM;MACN,QAAoC,KAAQ;AAG1C,YAAI,QAAQ,QAAW;AAErB,cAAI,WAAW,KAAK,OAAM;AAC1B,mBAAS,eAAc;AACvB,mBAAS,aAAa;YACpB,CAAC,iBAAiB,GAAG;aACpB,CAAC,iBAAiB,CAAC;AAEtB,eAAK;;MAET;;;AAIJ,SAAO;AACT;AAKA,SAAS,iBAAoB,KAAyB;AACpD,QAAM,SAA+B,CAAA;AAErC,aAAW,OAAO,KAAK;AACrB,WAAO,aAAa,GAAG,CAAC,IAAI,IAAI,GAAG;;AAGrC,SAAO;AACT;AAEA,SAAS,aAAa,GAAS;AAC7B,SAAO,EACJ,MAAM,GAAG,EACT,IAAI,CAAC,MAAM,UAAU,QAAQ,WAAW,IAAI,IAAI,IAAI,EACpD,KAAK,EAAE;AACZ;AAEA,SAAS,WAAW,GAAS;AAC3B,SAAO,EAAE,OAAO,CAAC,EAAE,YAAW,IAAK,EAAE,MAAM,CAAC;AAC9C;;;ACzLA,IAAA,eAAe;", "names": []}
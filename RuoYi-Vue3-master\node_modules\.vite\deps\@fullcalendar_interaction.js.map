{"version": 3, "sources": ["../../@fullcalendar/interaction/index.js"], "sourcesContent": ["import { createPlugin } from '@fullcalendar/core/index.js';\nimport { config, Emitter, elementClosest, applyStyle, whenTransitionDone, removeElement, ScrollController, ElementScrollController, computeInnerRect, WindowScrollController, ElementDragging, preventSelection, preventContextMenu, allowSelection, allowContextMenu, computeRect, getClippingParents, pointInsideRect, constrainPoint, intersectRects, getRectCenter, diffPoints, mapHash, rangeContainsRange, isDateSpansEqual, Interaction, interactionSettingsToStore, isDateSelectionValid, enableCursor, disableCursor, triggerDateSelect, compareNumbers, getElSeg, getRelevantEvents, EventImpl, createEmptyEventStore, applyMutationToEventStore, isInteractionValid, buildEventApis, interactionSettingsStore, startOfDay, diffDates, createDuration, getEventTargetViaRoot, identity, eventTupleToStore, parseDragMeta, elementMatches, refineEventDef, parseEventDef, getDefaultEventEnd, createEventInstance, BASE_OPTION_DEFAULTS } from '@fullcalendar/core/internal.js';\n\nconfig.touchMouseIgnoreWait = 500;\nlet ignoreMouseDepth = 0;\nlet listenerCnt = 0;\nlet isWindowTouchMoveCancelled = false;\n/*\nUses a \"pointer\" abstraction, which monitors UI events for both mouse and touch.\nTracks when the pointer \"drags\" on a certain element, meaning down+move+up.\n\nAlso, tracks if there was touch-scrolling.\nAlso, can prevent touch-scrolling from happening.\nAlso, can fire pointermove events when scrolling happens underneath, even when no real pointer movement.\n\nemits:\n- pointerdown\n- pointermove\n- pointerup\n*/\nclass PointerDragging {\n    constructor(containerEl) {\n        this.subjectEl = null;\n        // options that can be directly assigned by caller\n        this.selector = ''; // will cause subjectEl in all emitted events to be this element\n        this.handleSelector = '';\n        this.shouldIgnoreMove = false;\n        this.shouldWatchScroll = true; // for simulating pointermove on scroll\n        // internal states\n        this.isDragging = false;\n        this.isTouchDragging = false;\n        this.wasTouchScroll = false;\n        // Mouse\n        // ----------------------------------------------------------------------------------------------------\n        this.handleMouseDown = (ev) => {\n            if (!this.shouldIgnoreMouse() &&\n                isPrimaryMouseButton(ev) &&\n                this.tryStart(ev)) {\n                let pev = this.createEventFromMouse(ev, true);\n                this.emitter.trigger('pointerdown', pev);\n                this.initScrollWatch(pev);\n                if (!this.shouldIgnoreMove) {\n                    document.addEventListener('mousemove', this.handleMouseMove);\n                }\n                document.addEventListener('mouseup', this.handleMouseUp);\n            }\n        };\n        this.handleMouseMove = (ev) => {\n            let pev = this.createEventFromMouse(ev);\n            this.recordCoords(pev);\n            this.emitter.trigger('pointermove', pev);\n        };\n        this.handleMouseUp = (ev) => {\n            document.removeEventListener('mousemove', this.handleMouseMove);\n            document.removeEventListener('mouseup', this.handleMouseUp);\n            this.emitter.trigger('pointerup', this.createEventFromMouse(ev));\n            this.cleanup(); // call last so that pointerup has access to props\n        };\n        // Touch\n        // ----------------------------------------------------------------------------------------------------\n        this.handleTouchStart = (ev) => {\n            if (this.tryStart(ev)) {\n                this.isTouchDragging = true;\n                let pev = this.createEventFromTouch(ev, true);\n                this.emitter.trigger('pointerdown', pev);\n                this.initScrollWatch(pev);\n                // unlike mouse, need to attach to target, not document\n                // https://stackoverflow.com/a/45760014\n                let targetEl = ev.target;\n                if (!this.shouldIgnoreMove) {\n                    targetEl.addEventListener('touchmove', this.handleTouchMove);\n                }\n                targetEl.addEventListener('touchend', this.handleTouchEnd);\n                targetEl.addEventListener('touchcancel', this.handleTouchEnd); // treat it as a touch end\n                // attach a handler to get called when ANY scroll action happens on the page.\n                // this was impossible to do with normal on/off because 'scroll' doesn't bubble.\n                // http://stackoverflow.com/a/32954565/96342\n                window.addEventListener('scroll', this.handleTouchScroll, true);\n            }\n        };\n        this.handleTouchMove = (ev) => {\n            let pev = this.createEventFromTouch(ev);\n            this.recordCoords(pev);\n            this.emitter.trigger('pointermove', pev);\n        };\n        this.handleTouchEnd = (ev) => {\n            if (this.isDragging) { // done to guard against touchend followed by touchcancel\n                let targetEl = ev.target;\n                targetEl.removeEventListener('touchmove', this.handleTouchMove);\n                targetEl.removeEventListener('touchend', this.handleTouchEnd);\n                targetEl.removeEventListener('touchcancel', this.handleTouchEnd);\n                window.removeEventListener('scroll', this.handleTouchScroll, true); // useCaptured=true\n                this.emitter.trigger('pointerup', this.createEventFromTouch(ev));\n                this.cleanup(); // call last so that pointerup has access to props\n                this.isTouchDragging = false;\n                startIgnoringMouse();\n            }\n        };\n        this.handleTouchScroll = () => {\n            this.wasTouchScroll = true;\n        };\n        this.handleScroll = (ev) => {\n            if (!this.shouldIgnoreMove) {\n                let pageX = (window.scrollX - this.prevScrollX) + this.prevPageX;\n                let pageY = (window.scrollY - this.prevScrollY) + this.prevPageY;\n                this.emitter.trigger('pointermove', {\n                    origEvent: ev,\n                    isTouch: this.isTouchDragging,\n                    subjectEl: this.subjectEl,\n                    pageX,\n                    pageY,\n                    deltaX: pageX - this.origPageX,\n                    deltaY: pageY - this.origPageY,\n                });\n            }\n        };\n        this.containerEl = containerEl;\n        this.emitter = new Emitter();\n        containerEl.addEventListener('mousedown', this.handleMouseDown);\n        containerEl.addEventListener('touchstart', this.handleTouchStart, { passive: true });\n        listenerCreated();\n    }\n    destroy() {\n        this.containerEl.removeEventListener('mousedown', this.handleMouseDown);\n        this.containerEl.removeEventListener('touchstart', this.handleTouchStart, { passive: true });\n        listenerDestroyed();\n    }\n    tryStart(ev) {\n        let subjectEl = this.querySubjectEl(ev);\n        let downEl = ev.target;\n        if (subjectEl &&\n            (!this.handleSelector || elementClosest(downEl, this.handleSelector))) {\n            this.subjectEl = subjectEl;\n            this.isDragging = true; // do this first so cancelTouchScroll will work\n            this.wasTouchScroll = false;\n            return true;\n        }\n        return false;\n    }\n    cleanup() {\n        isWindowTouchMoveCancelled = false;\n        this.isDragging = false;\n        this.subjectEl = null;\n        // keep wasTouchScroll around for later access\n        this.destroyScrollWatch();\n    }\n    querySubjectEl(ev) {\n        if (this.selector) {\n            return elementClosest(ev.target, this.selector);\n        }\n        return this.containerEl;\n    }\n    shouldIgnoreMouse() {\n        return ignoreMouseDepth || this.isTouchDragging;\n    }\n    // can be called by user of this class, to cancel touch-based scrolling for the current drag\n    cancelTouchScroll() {\n        if (this.isDragging) {\n            isWindowTouchMoveCancelled = true;\n        }\n    }\n    // Scrolling that simulates pointermoves\n    // ----------------------------------------------------------------------------------------------------\n    initScrollWatch(ev) {\n        if (this.shouldWatchScroll) {\n            this.recordCoords(ev);\n            window.addEventListener('scroll', this.handleScroll, true); // useCapture=true\n        }\n    }\n    recordCoords(ev) {\n        if (this.shouldWatchScroll) {\n            this.prevPageX = ev.pageX;\n            this.prevPageY = ev.pageY;\n            this.prevScrollX = window.scrollX;\n            this.prevScrollY = window.scrollY;\n        }\n    }\n    destroyScrollWatch() {\n        if (this.shouldWatchScroll) {\n            window.removeEventListener('scroll', this.handleScroll, true); // useCaptured=true\n        }\n    }\n    // Event Normalization\n    // ----------------------------------------------------------------------------------------------------\n    createEventFromMouse(ev, isFirst) {\n        let deltaX = 0;\n        let deltaY = 0;\n        // TODO: repeat code\n        if (isFirst) {\n            this.origPageX = ev.pageX;\n            this.origPageY = ev.pageY;\n        }\n        else {\n            deltaX = ev.pageX - this.origPageX;\n            deltaY = ev.pageY - this.origPageY;\n        }\n        return {\n            origEvent: ev,\n            isTouch: false,\n            subjectEl: this.subjectEl,\n            pageX: ev.pageX,\n            pageY: ev.pageY,\n            deltaX,\n            deltaY,\n        };\n    }\n    createEventFromTouch(ev, isFirst) {\n        let touches = ev.touches;\n        let pageX;\n        let pageY;\n        let deltaX = 0;\n        let deltaY = 0;\n        // if touch coords available, prefer,\n        // because FF would give bad ev.pageX ev.pageY\n        if (touches && touches.length) {\n            pageX = touches[0].pageX;\n            pageY = touches[0].pageY;\n        }\n        else {\n            pageX = ev.pageX;\n            pageY = ev.pageY;\n        }\n        // TODO: repeat code\n        if (isFirst) {\n            this.origPageX = pageX;\n            this.origPageY = pageY;\n        }\n        else {\n            deltaX = pageX - this.origPageX;\n            deltaY = pageY - this.origPageY;\n        }\n        return {\n            origEvent: ev,\n            isTouch: true,\n            subjectEl: this.subjectEl,\n            pageX,\n            pageY,\n            deltaX,\n            deltaY,\n        };\n    }\n}\n// Returns a boolean whether this was a left mouse click and no ctrl key (which means right click on Mac)\nfunction isPrimaryMouseButton(ev) {\n    return ev.button === 0 && !ev.ctrlKey;\n}\n// Ignoring fake mouse events generated by touch\n// ----------------------------------------------------------------------------------------------------\nfunction startIgnoringMouse() {\n    ignoreMouseDepth += 1;\n    setTimeout(() => {\n        ignoreMouseDepth -= 1;\n    }, config.touchMouseIgnoreWait);\n}\n// We want to attach touchmove as early as possible for Safari\n// ----------------------------------------------------------------------------------------------------\nfunction listenerCreated() {\n    listenerCnt += 1;\n    if (listenerCnt === 1) {\n        window.addEventListener('touchmove', onWindowTouchMove, { passive: false });\n    }\n}\nfunction listenerDestroyed() {\n    listenerCnt -= 1;\n    if (!listenerCnt) {\n        window.removeEventListener('touchmove', onWindowTouchMove, { passive: false });\n    }\n}\nfunction onWindowTouchMove(ev) {\n    if (isWindowTouchMoveCancelled) {\n        ev.preventDefault();\n    }\n}\n\n/*\nAn effect in which an element follows the movement of a pointer across the screen.\nThe moving element is a clone of some other element.\nMust call start + handleMove + stop.\n*/\nclass ElementMirror {\n    constructor() {\n        this.isVisible = false; // must be explicitly enabled\n        this.sourceEl = null;\n        this.mirrorEl = null;\n        this.sourceElRect = null; // screen coords relative to viewport\n        // options that can be set directly by caller\n        this.parentNode = document.body; // HIGHLY SUGGESTED to set this to sidestep ShadowDOM issues\n        this.zIndex = 9999;\n        this.revertDuration = 0;\n    }\n    start(sourceEl, pageX, pageY) {\n        this.sourceEl = sourceEl;\n        this.sourceElRect = this.sourceEl.getBoundingClientRect();\n        this.origScreenX = pageX - window.scrollX;\n        this.origScreenY = pageY - window.scrollY;\n        this.deltaX = 0;\n        this.deltaY = 0;\n        this.updateElPosition();\n    }\n    handleMove(pageX, pageY) {\n        this.deltaX = (pageX - window.scrollX) - this.origScreenX;\n        this.deltaY = (pageY - window.scrollY) - this.origScreenY;\n        this.updateElPosition();\n    }\n    // can be called before start\n    setIsVisible(bool) {\n        if (bool) {\n            if (!this.isVisible) {\n                if (this.mirrorEl) {\n                    this.mirrorEl.style.display = '';\n                }\n                this.isVisible = bool; // needs to happen before updateElPosition\n                this.updateElPosition(); // because was not updating the position while invisible\n            }\n        }\n        else if (this.isVisible) {\n            if (this.mirrorEl) {\n                this.mirrorEl.style.display = 'none';\n            }\n            this.isVisible = bool;\n        }\n    }\n    // always async\n    stop(needsRevertAnimation, callback) {\n        let done = () => {\n            this.cleanup();\n            callback();\n        };\n        if (needsRevertAnimation &&\n            this.mirrorEl &&\n            this.isVisible &&\n            this.revertDuration && // if 0, transition won't work\n            (this.deltaX || this.deltaY) // if same coords, transition won't work\n        ) {\n            this.doRevertAnimation(done, this.revertDuration);\n        }\n        else {\n            setTimeout(done, 0);\n        }\n    }\n    doRevertAnimation(callback, revertDuration) {\n        let mirrorEl = this.mirrorEl;\n        let finalSourceElRect = this.sourceEl.getBoundingClientRect(); // because autoscrolling might have happened\n        mirrorEl.style.transition =\n            'top ' + revertDuration + 'ms,' +\n                'left ' + revertDuration + 'ms';\n        applyStyle(mirrorEl, {\n            left: finalSourceElRect.left,\n            top: finalSourceElRect.top,\n        });\n        whenTransitionDone(mirrorEl, () => {\n            mirrorEl.style.transition = '';\n            callback();\n        });\n    }\n    cleanup() {\n        if (this.mirrorEl) {\n            removeElement(this.mirrorEl);\n            this.mirrorEl = null;\n        }\n        this.sourceEl = null;\n    }\n    updateElPosition() {\n        if (this.sourceEl && this.isVisible) {\n            applyStyle(this.getMirrorEl(), {\n                left: this.sourceElRect.left + this.deltaX,\n                top: this.sourceElRect.top + this.deltaY,\n            });\n        }\n    }\n    getMirrorEl() {\n        let sourceElRect = this.sourceElRect;\n        let mirrorEl = this.mirrorEl;\n        if (!mirrorEl) {\n            mirrorEl = this.mirrorEl = this.sourceEl.cloneNode(true); // cloneChildren=true\n            // we don't want long taps or any mouse interaction causing selection/menus.\n            // would use preventSelection(), but that prevents selectstart, causing problems.\n            mirrorEl.style.userSelect = 'none';\n            mirrorEl.style.webkitUserSelect = 'none';\n            mirrorEl.style.pointerEvents = 'none';\n            mirrorEl.classList.add('fc-event-dragging');\n            applyStyle(mirrorEl, {\n                position: 'fixed',\n                zIndex: this.zIndex,\n                visibility: '',\n                boxSizing: 'border-box',\n                width: sourceElRect.right - sourceElRect.left,\n                height: sourceElRect.bottom - sourceElRect.top,\n                right: 'auto',\n                bottom: 'auto',\n                margin: 0,\n            });\n            this.parentNode.appendChild(mirrorEl);\n        }\n        return mirrorEl;\n    }\n}\n\n/*\nIs a cache for a given element's scroll information (all the info that ScrollController stores)\nin addition the \"client rectangle\" of the element.. the area within the scrollbars.\n\nThe cache can be in one of two modes:\n- doesListening:false - ignores when the container is scrolled by someone else\n- doesListening:true - watch for scrolling and update the cache\n*/\nclass ScrollGeomCache extends ScrollController {\n    constructor(scrollController, doesListening) {\n        super();\n        this.handleScroll = () => {\n            this.scrollTop = this.scrollController.getScrollTop();\n            this.scrollLeft = this.scrollController.getScrollLeft();\n            this.handleScrollChange();\n        };\n        this.scrollController = scrollController;\n        this.doesListening = doesListening;\n        this.scrollTop = this.origScrollTop = scrollController.getScrollTop();\n        this.scrollLeft = this.origScrollLeft = scrollController.getScrollLeft();\n        this.scrollWidth = scrollController.getScrollWidth();\n        this.scrollHeight = scrollController.getScrollHeight();\n        this.clientWidth = scrollController.getClientWidth();\n        this.clientHeight = scrollController.getClientHeight();\n        this.clientRect = this.computeClientRect(); // do last in case it needs cached values\n        if (this.doesListening) {\n            this.getEventTarget().addEventListener('scroll', this.handleScroll);\n        }\n    }\n    destroy() {\n        if (this.doesListening) {\n            this.getEventTarget().removeEventListener('scroll', this.handleScroll);\n        }\n    }\n    getScrollTop() {\n        return this.scrollTop;\n    }\n    getScrollLeft() {\n        return this.scrollLeft;\n    }\n    setScrollTop(top) {\n        this.scrollController.setScrollTop(top);\n        if (!this.doesListening) {\n            // we are not relying on the element to normalize out-of-bounds scroll values\n            // so we need to sanitize ourselves\n            this.scrollTop = Math.max(Math.min(top, this.getMaxScrollTop()), 0);\n            this.handleScrollChange();\n        }\n    }\n    setScrollLeft(top) {\n        this.scrollController.setScrollLeft(top);\n        if (!this.doesListening) {\n            // we are not relying on the element to normalize out-of-bounds scroll values\n            // so we need to sanitize ourselves\n            this.scrollLeft = Math.max(Math.min(top, this.getMaxScrollLeft()), 0);\n            this.handleScrollChange();\n        }\n    }\n    getClientWidth() {\n        return this.clientWidth;\n    }\n    getClientHeight() {\n        return this.clientHeight;\n    }\n    getScrollWidth() {\n        return this.scrollWidth;\n    }\n    getScrollHeight() {\n        return this.scrollHeight;\n    }\n    handleScrollChange() {\n    }\n}\n\nclass ElementScrollGeomCache extends ScrollGeomCache {\n    constructor(el, doesListening) {\n        super(new ElementScrollController(el), doesListening);\n    }\n    getEventTarget() {\n        return this.scrollController.el;\n    }\n    computeClientRect() {\n        return computeInnerRect(this.scrollController.el);\n    }\n}\n\nclass WindowScrollGeomCache extends ScrollGeomCache {\n    constructor(doesListening) {\n        super(new WindowScrollController(), doesListening);\n    }\n    getEventTarget() {\n        return window;\n    }\n    computeClientRect() {\n        return {\n            left: this.scrollLeft,\n            right: this.scrollLeft + this.clientWidth,\n            top: this.scrollTop,\n            bottom: this.scrollTop + this.clientHeight,\n        };\n    }\n    // the window is the only scroll object that changes it's rectangle relative\n    // to the document's topleft as it scrolls\n    handleScrollChange() {\n        this.clientRect = this.computeClientRect();\n    }\n}\n\n// If available we are using native \"performance\" API instead of \"Date\"\n// Read more about it on MDN:\n// https://developer.mozilla.org/en-US/docs/Web/API/Performance\nconst getTime = typeof performance === 'function' ? performance.now : Date.now;\n/*\nFor a pointer interaction, automatically scrolls certain scroll containers when the pointer\napproaches the edge.\n\nThe caller must call start + handleMove + stop.\n*/\nclass AutoScroller {\n    constructor() {\n        // options that can be set by caller\n        this.isEnabled = true;\n        this.scrollQuery = [window, '.fc-scroller'];\n        this.edgeThreshold = 50; // pixels\n        this.maxVelocity = 300; // pixels per second\n        // internal state\n        this.pointerScreenX = null;\n        this.pointerScreenY = null;\n        this.isAnimating = false;\n        this.scrollCaches = null;\n        // protect against the initial pointerdown being too close to an edge and starting the scroll\n        this.everMovedUp = false;\n        this.everMovedDown = false;\n        this.everMovedLeft = false;\n        this.everMovedRight = false;\n        this.animate = () => {\n            if (this.isAnimating) { // wasn't cancelled between animation calls\n                let edge = this.computeBestEdge(this.pointerScreenX + window.scrollX, this.pointerScreenY + window.scrollY);\n                if (edge) {\n                    let now = getTime();\n                    this.handleSide(edge, (now - this.msSinceRequest) / 1000);\n                    this.requestAnimation(now);\n                }\n                else {\n                    this.isAnimating = false; // will stop animation\n                }\n            }\n        };\n    }\n    start(pageX, pageY, scrollStartEl) {\n        if (this.isEnabled) {\n            this.scrollCaches = this.buildCaches(scrollStartEl);\n            this.pointerScreenX = null;\n            this.pointerScreenY = null;\n            this.everMovedUp = false;\n            this.everMovedDown = false;\n            this.everMovedLeft = false;\n            this.everMovedRight = false;\n            this.handleMove(pageX, pageY);\n        }\n    }\n    handleMove(pageX, pageY) {\n        if (this.isEnabled) {\n            let pointerScreenX = pageX - window.scrollX;\n            let pointerScreenY = pageY - window.scrollY;\n            let yDelta = this.pointerScreenY === null ? 0 : pointerScreenY - this.pointerScreenY;\n            let xDelta = this.pointerScreenX === null ? 0 : pointerScreenX - this.pointerScreenX;\n            if (yDelta < 0) {\n                this.everMovedUp = true;\n            }\n            else if (yDelta > 0) {\n                this.everMovedDown = true;\n            }\n            if (xDelta < 0) {\n                this.everMovedLeft = true;\n            }\n            else if (xDelta > 0) {\n                this.everMovedRight = true;\n            }\n            this.pointerScreenX = pointerScreenX;\n            this.pointerScreenY = pointerScreenY;\n            if (!this.isAnimating) {\n                this.isAnimating = true;\n                this.requestAnimation(getTime());\n            }\n        }\n    }\n    stop() {\n        if (this.isEnabled) {\n            this.isAnimating = false; // will stop animation\n            for (let scrollCache of this.scrollCaches) {\n                scrollCache.destroy();\n            }\n            this.scrollCaches = null;\n        }\n    }\n    requestAnimation(now) {\n        this.msSinceRequest = now;\n        requestAnimationFrame(this.animate);\n    }\n    handleSide(edge, seconds) {\n        let { scrollCache } = edge;\n        let { edgeThreshold } = this;\n        let invDistance = edgeThreshold - edge.distance;\n        let velocity = // the closer to the edge, the faster we scroll\n         ((invDistance * invDistance) / (edgeThreshold * edgeThreshold)) * // quadratic\n            this.maxVelocity * seconds;\n        let sign = 1;\n        switch (edge.name) {\n            case 'left':\n                sign = -1;\n            // falls through\n            case 'right':\n                scrollCache.setScrollLeft(scrollCache.getScrollLeft() + velocity * sign);\n                break;\n            case 'top':\n                sign = -1;\n            // falls through\n            case 'bottom':\n                scrollCache.setScrollTop(scrollCache.getScrollTop() + velocity * sign);\n                break;\n        }\n    }\n    // left/top are relative to document topleft\n    computeBestEdge(left, top) {\n        let { edgeThreshold } = this;\n        let bestSide = null;\n        let scrollCaches = this.scrollCaches || [];\n        for (let scrollCache of scrollCaches) {\n            let rect = scrollCache.clientRect;\n            let leftDist = left - rect.left;\n            let rightDist = rect.right - left;\n            let topDist = top - rect.top;\n            let bottomDist = rect.bottom - top;\n            // completely within the rect?\n            if (leftDist >= 0 && rightDist >= 0 && topDist >= 0 && bottomDist >= 0) {\n                if (topDist <= edgeThreshold && this.everMovedUp && scrollCache.canScrollUp() &&\n                    (!bestSide || bestSide.distance > topDist)) {\n                    bestSide = { scrollCache, name: 'top', distance: topDist };\n                }\n                if (bottomDist <= edgeThreshold && this.everMovedDown && scrollCache.canScrollDown() &&\n                    (!bestSide || bestSide.distance > bottomDist)) {\n                    bestSide = { scrollCache, name: 'bottom', distance: bottomDist };\n                }\n                /*\n                TODO: fix broken RTL scrolling. canScrollLeft always returning false\n                https://github.com/fullcalendar/fullcalendar/issues/4837\n                */\n                if (leftDist <= edgeThreshold && this.everMovedLeft && scrollCache.canScrollLeft() &&\n                    (!bestSide || bestSide.distance > leftDist)) {\n                    bestSide = { scrollCache, name: 'left', distance: leftDist };\n                }\n                if (rightDist <= edgeThreshold && this.everMovedRight && scrollCache.canScrollRight() &&\n                    (!bestSide || bestSide.distance > rightDist)) {\n                    bestSide = { scrollCache, name: 'right', distance: rightDist };\n                }\n            }\n        }\n        return bestSide;\n    }\n    buildCaches(scrollStartEl) {\n        return this.queryScrollEls(scrollStartEl).map((el) => {\n            if (el === window) {\n                return new WindowScrollGeomCache(false); // false = don't listen to user-generated scrolls\n            }\n            return new ElementScrollGeomCache(el, false); // false = don't listen to user-generated scrolls\n        });\n    }\n    queryScrollEls(scrollStartEl) {\n        let els = [];\n        for (let query of this.scrollQuery) {\n            if (typeof query === 'object') {\n                els.push(query);\n            }\n            else {\n                /*\n                TODO: in the future, always have auto-scroll happen on element where current Hit came from\n                Ticket: https://github.com/fullcalendar/fullcalendar/issues/4593\n                */\n                els.push(...Array.prototype.slice.call(scrollStartEl.getRootNode().querySelectorAll(query)));\n            }\n        }\n        return els;\n    }\n}\n\n/*\nMonitors dragging on an element. Has a number of high-level features:\n- minimum distance required before dragging\n- minimum wait time (\"delay\") before dragging\n- a mirror element that follows the pointer\n*/\nclass FeaturefulElementDragging extends ElementDragging {\n    constructor(containerEl, selector) {\n        super(containerEl);\n        this.containerEl = containerEl;\n        // options that can be directly set by caller\n        // the caller can also set the PointerDragging's options as well\n        this.delay = null;\n        this.minDistance = 0;\n        this.touchScrollAllowed = true; // prevents drag from starting and blocks scrolling during drag\n        this.mirrorNeedsRevert = false;\n        this.isInteracting = false; // is the user validly moving the pointer? lasts until pointerup\n        this.isDragging = false; // is it INTENTFULLY dragging? lasts until after revert animation\n        this.isDelayEnded = false;\n        this.isDistanceSurpassed = false;\n        this.delayTimeoutId = null;\n        this.onPointerDown = (ev) => {\n            if (!this.isDragging) { // so new drag doesn't happen while revert animation is going\n                this.isInteracting = true;\n                this.isDelayEnded = false;\n                this.isDistanceSurpassed = false;\n                preventSelection(document.body);\n                preventContextMenu(document.body);\n                // prevent links from being visited if there's an eventual drag.\n                // also prevents selection in older browsers (maybe?).\n                // not necessary for touch, besides, browser would complain about passiveness.\n                if (!ev.isTouch) {\n                    ev.origEvent.preventDefault();\n                }\n                this.emitter.trigger('pointerdown', ev);\n                if (this.isInteracting && // not destroyed via pointerdown handler\n                    !this.pointer.shouldIgnoreMove) {\n                    // actions related to initiating dragstart+dragmove+dragend...\n                    this.mirror.setIsVisible(false); // reset. caller must set-visible\n                    this.mirror.start(ev.subjectEl, ev.pageX, ev.pageY); // must happen on first pointer down\n                    this.startDelay(ev);\n                    if (!this.minDistance) {\n                        this.handleDistanceSurpassed(ev);\n                    }\n                }\n            }\n        };\n        this.onPointerMove = (ev) => {\n            if (this.isInteracting) {\n                this.emitter.trigger('pointermove', ev);\n                if (!this.isDistanceSurpassed) {\n                    let minDistance = this.minDistance;\n                    let distanceSq; // current distance from the origin, squared\n                    let { deltaX, deltaY } = ev;\n                    distanceSq = deltaX * deltaX + deltaY * deltaY;\n                    if (distanceSq >= minDistance * minDistance) { // use pythagorean theorem\n                        this.handleDistanceSurpassed(ev);\n                    }\n                }\n                if (this.isDragging) {\n                    // a real pointer move? (not one simulated by scrolling)\n                    if (ev.origEvent.type !== 'scroll') {\n                        this.mirror.handleMove(ev.pageX, ev.pageY);\n                        this.autoScroller.handleMove(ev.pageX, ev.pageY);\n                    }\n                    this.emitter.trigger('dragmove', ev);\n                }\n            }\n        };\n        this.onPointerUp = (ev) => {\n            if (this.isInteracting) {\n                this.isInteracting = false;\n                allowSelection(document.body);\n                allowContextMenu(document.body);\n                this.emitter.trigger('pointerup', ev); // can potentially set mirrorNeedsRevert\n                if (this.isDragging) {\n                    this.autoScroller.stop();\n                    this.tryStopDrag(ev); // which will stop the mirror\n                }\n                if (this.delayTimeoutId) {\n                    clearTimeout(this.delayTimeoutId);\n                    this.delayTimeoutId = null;\n                }\n            }\n        };\n        let pointer = this.pointer = new PointerDragging(containerEl);\n        pointer.emitter.on('pointerdown', this.onPointerDown);\n        pointer.emitter.on('pointermove', this.onPointerMove);\n        pointer.emitter.on('pointerup', this.onPointerUp);\n        if (selector) {\n            pointer.selector = selector;\n        }\n        this.mirror = new ElementMirror();\n        this.autoScroller = new AutoScroller();\n    }\n    destroy() {\n        this.pointer.destroy();\n        // HACK: simulate a pointer-up to end the current drag\n        // TODO: fire 'dragend' directly and stop interaction. discourage use of pointerup event (b/c might not fire)\n        this.onPointerUp({});\n    }\n    startDelay(ev) {\n        if (typeof this.delay === 'number') {\n            this.delayTimeoutId = setTimeout(() => {\n                this.delayTimeoutId = null;\n                this.handleDelayEnd(ev);\n            }, this.delay); // not assignable to number!\n        }\n        else {\n            this.handleDelayEnd(ev);\n        }\n    }\n    handleDelayEnd(ev) {\n        this.isDelayEnded = true;\n        this.tryStartDrag(ev);\n    }\n    handleDistanceSurpassed(ev) {\n        this.isDistanceSurpassed = true;\n        this.tryStartDrag(ev);\n    }\n    tryStartDrag(ev) {\n        if (this.isDelayEnded && this.isDistanceSurpassed) {\n            if (!this.pointer.wasTouchScroll || this.touchScrollAllowed) {\n                this.isDragging = true;\n                this.mirrorNeedsRevert = false;\n                this.autoScroller.start(ev.pageX, ev.pageY, this.containerEl);\n                this.emitter.trigger('dragstart', ev);\n                if (this.touchScrollAllowed === false) {\n                    this.pointer.cancelTouchScroll();\n                }\n            }\n        }\n    }\n    tryStopDrag(ev) {\n        // .stop() is ALWAYS asynchronous, which we NEED because we want all pointerup events\n        // that come from the document to fire beforehand. much more convenient this way.\n        this.mirror.stop(this.mirrorNeedsRevert, this.stopDrag.bind(this, ev));\n    }\n    stopDrag(ev) {\n        this.isDragging = false;\n        this.emitter.trigger('dragend', ev);\n    }\n    // fill in the implementations...\n    setIgnoreMove(bool) {\n        this.pointer.shouldIgnoreMove = bool;\n    }\n    setMirrorIsVisible(bool) {\n        this.mirror.setIsVisible(bool);\n    }\n    setMirrorNeedsRevert(bool) {\n        this.mirrorNeedsRevert = bool;\n    }\n    setAutoScrollEnabled(bool) {\n        this.autoScroller.isEnabled = bool;\n    }\n}\n\n/*\nWhen this class is instantiated, it records the offset of an element (relative to the document topleft),\nand continues to monitor scrolling, updating the cached coordinates if it needs to.\nDoes not access the DOM after instantiation, so highly performant.\n\nAlso keeps track of all scrolling/overflow:hidden containers that are parents of the given element\nand an determine if a given point is inside the combined clipping rectangle.\n*/\nclass OffsetTracker {\n    constructor(el) {\n        this.el = el;\n        this.origRect = computeRect(el);\n        // will work fine for divs that have overflow:hidden\n        this.scrollCaches = getClippingParents(el).map((scrollEl) => new ElementScrollGeomCache(scrollEl, true));\n    }\n    destroy() {\n        for (let scrollCache of this.scrollCaches) {\n            scrollCache.destroy();\n        }\n    }\n    computeLeft() {\n        let left = this.origRect.left;\n        for (let scrollCache of this.scrollCaches) {\n            left += scrollCache.origScrollLeft - scrollCache.getScrollLeft();\n        }\n        return left;\n    }\n    computeTop() {\n        let top = this.origRect.top;\n        for (let scrollCache of this.scrollCaches) {\n            top += scrollCache.origScrollTop - scrollCache.getScrollTop();\n        }\n        return top;\n    }\n    isWithinClipping(pageX, pageY) {\n        let point = { left: pageX, top: pageY };\n        for (let scrollCache of this.scrollCaches) {\n            if (!isIgnoredClipping(scrollCache.getEventTarget()) &&\n                !pointInsideRect(point, scrollCache.clientRect)) {\n                return false;\n            }\n        }\n        return true;\n    }\n}\n// certain clipping containers should never constrain interactions, like <html> and <body>\n// https://github.com/fullcalendar/fullcalendar/issues/3615\nfunction isIgnoredClipping(node) {\n    let tagName = node.tagName;\n    return tagName === 'HTML' || tagName === 'BODY';\n}\n\n/*\nTracks movement over multiple droppable areas (aka \"hits\")\nthat exist in one or more DateComponents.\nRelies on an existing draggable.\n\nemits:\n- pointerdown\n- dragstart\n- hitchange - fires initially, even if not over a hit\n- pointerup\n- (hitchange - again, to null, if ended over a hit)\n- dragend\n*/\nclass HitDragging {\n    constructor(dragging, droppableStore) {\n        // options that can be set by caller\n        this.useSubjectCenter = false;\n        this.requireInitial = true; // if doesn't start out on a hit, won't emit any events\n        this.disablePointCheck = false;\n        this.initialHit = null;\n        this.movingHit = null;\n        this.finalHit = null; // won't ever be populated if shouldIgnoreMove\n        this.handlePointerDown = (ev) => {\n            let { dragging } = this;\n            this.initialHit = null;\n            this.movingHit = null;\n            this.finalHit = null;\n            this.prepareHits();\n            this.processFirstCoord(ev);\n            if (this.initialHit || !this.requireInitial) {\n                dragging.setIgnoreMove(false);\n                // TODO: fire this before computing processFirstCoord, so listeners can cancel. this gets fired by almost every handler :(\n                this.emitter.trigger('pointerdown', ev);\n            }\n            else {\n                dragging.setIgnoreMove(true);\n            }\n        };\n        this.handleDragStart = (ev) => {\n            this.emitter.trigger('dragstart', ev);\n            this.handleMove(ev, true); // force = fire even if initially null\n        };\n        this.handleDragMove = (ev) => {\n            this.emitter.trigger('dragmove', ev);\n            this.handleMove(ev);\n        };\n        this.handlePointerUp = (ev) => {\n            this.releaseHits();\n            this.emitter.trigger('pointerup', ev);\n        };\n        this.handleDragEnd = (ev) => {\n            if (this.movingHit) {\n                this.emitter.trigger('hitupdate', null, true, ev);\n            }\n            this.finalHit = this.movingHit;\n            this.movingHit = null;\n            this.emitter.trigger('dragend', ev);\n        };\n        this.droppableStore = droppableStore;\n        dragging.emitter.on('pointerdown', this.handlePointerDown);\n        dragging.emitter.on('dragstart', this.handleDragStart);\n        dragging.emitter.on('dragmove', this.handleDragMove);\n        dragging.emitter.on('pointerup', this.handlePointerUp);\n        dragging.emitter.on('dragend', this.handleDragEnd);\n        this.dragging = dragging;\n        this.emitter = new Emitter();\n    }\n    // sets initialHit\n    // sets coordAdjust\n    processFirstCoord(ev) {\n        let origPoint = { left: ev.pageX, top: ev.pageY };\n        let adjustedPoint = origPoint;\n        let subjectEl = ev.subjectEl;\n        let subjectRect;\n        if (subjectEl instanceof HTMLElement) { // i.e. not a Document/ShadowRoot\n            subjectRect = computeRect(subjectEl);\n            adjustedPoint = constrainPoint(adjustedPoint, subjectRect);\n        }\n        let initialHit = this.initialHit = this.queryHitForOffset(adjustedPoint.left, adjustedPoint.top);\n        if (initialHit) {\n            if (this.useSubjectCenter && subjectRect) {\n                let slicedSubjectRect = intersectRects(subjectRect, initialHit.rect);\n                if (slicedSubjectRect) {\n                    adjustedPoint = getRectCenter(slicedSubjectRect);\n                }\n            }\n            this.coordAdjust = diffPoints(adjustedPoint, origPoint);\n        }\n        else {\n            this.coordAdjust = { left: 0, top: 0 };\n        }\n    }\n    handleMove(ev, forceHandle) {\n        let hit = this.queryHitForOffset(ev.pageX + this.coordAdjust.left, ev.pageY + this.coordAdjust.top);\n        if (forceHandle || !isHitsEqual(this.movingHit, hit)) {\n            this.movingHit = hit;\n            this.emitter.trigger('hitupdate', hit, false, ev);\n        }\n    }\n    prepareHits() {\n        this.offsetTrackers = mapHash(this.droppableStore, (interactionSettings) => {\n            interactionSettings.component.prepareHits();\n            return new OffsetTracker(interactionSettings.el);\n        });\n    }\n    releaseHits() {\n        let { offsetTrackers } = this;\n        for (let id in offsetTrackers) {\n            offsetTrackers[id].destroy();\n        }\n        this.offsetTrackers = {};\n    }\n    queryHitForOffset(offsetLeft, offsetTop) {\n        let { droppableStore, offsetTrackers } = this;\n        let bestHit = null;\n        for (let id in droppableStore) {\n            let component = droppableStore[id].component;\n            let offsetTracker = offsetTrackers[id];\n            if (offsetTracker && // wasn't destroyed mid-drag\n                offsetTracker.isWithinClipping(offsetLeft, offsetTop)) {\n                let originLeft = offsetTracker.computeLeft();\n                let originTop = offsetTracker.computeTop();\n                let positionLeft = offsetLeft - originLeft;\n                let positionTop = offsetTop - originTop;\n                let { origRect } = offsetTracker;\n                let width = origRect.right - origRect.left;\n                let height = origRect.bottom - origRect.top;\n                if (\n                // must be within the element's bounds\n                positionLeft >= 0 && positionLeft < width &&\n                    positionTop >= 0 && positionTop < height) {\n                    let hit = component.queryHit(positionLeft, positionTop, width, height);\n                    if (hit && (\n                    // make sure the hit is within activeRange, meaning it's not a dead cell\n                    rangeContainsRange(hit.dateProfile.activeRange, hit.dateSpan.range)) &&\n                        // Ensure the component we are querying for the hit is accessibly my the pointer\n                        // Prevents obscured calendars (ex: under a modal dialog) from accepting hit\n                        // https://github.com/fullcalendar/fullcalendar/issues/5026\n                        (this.disablePointCheck ||\n                            offsetTracker.el.contains(offsetTracker.el.getRootNode().elementFromPoint(\n                            // add-back origins to get coordinate relative to top-left of window viewport\n                            positionLeft + originLeft - window.scrollX, positionTop + originTop - window.scrollY))) &&\n                        (!bestHit || hit.layer > bestHit.layer)) {\n                        hit.componentId = id;\n                        hit.context = component.context;\n                        // TODO: better way to re-orient rectangle\n                        hit.rect.left += originLeft;\n                        hit.rect.right += originLeft;\n                        hit.rect.top += originTop;\n                        hit.rect.bottom += originTop;\n                        bestHit = hit;\n                    }\n                }\n            }\n        }\n        return bestHit;\n    }\n}\nfunction isHitsEqual(hit0, hit1) {\n    if (!hit0 && !hit1) {\n        return true;\n    }\n    if (Boolean(hit0) !== Boolean(hit1)) {\n        return false;\n    }\n    return isDateSpansEqual(hit0.dateSpan, hit1.dateSpan);\n}\n\nfunction buildDatePointApiWithContext(dateSpan, context) {\n    let props = {};\n    for (let transform of context.pluginHooks.datePointTransforms) {\n        Object.assign(props, transform(dateSpan, context));\n    }\n    Object.assign(props, buildDatePointApi(dateSpan, context.dateEnv));\n    return props;\n}\nfunction buildDatePointApi(span, dateEnv) {\n    return {\n        date: dateEnv.toDate(span.range.start),\n        dateStr: dateEnv.formatIso(span.range.start, { omitTime: span.allDay }),\n        allDay: span.allDay,\n    };\n}\n\n/*\nMonitors when the user clicks on a specific date/time of a component.\nA pointerdown+pointerup on the same \"hit\" constitutes a click.\n*/\nclass DateClicking extends Interaction {\n    constructor(settings) {\n        super(settings);\n        this.handlePointerDown = (pev) => {\n            let { dragging } = this;\n            let downEl = pev.origEvent.target;\n            // do this in pointerdown (not dragend) because DOM might be mutated by the time dragend is fired\n            dragging.setIgnoreMove(!this.component.isValidDateDownEl(downEl));\n        };\n        // won't even fire if moving was ignored\n        this.handleDragEnd = (ev) => {\n            let { component } = this;\n            let { pointer } = this.dragging;\n            if (!pointer.wasTouchScroll) {\n                let { initialHit, finalHit } = this.hitDragging;\n                if (initialHit && finalHit && isHitsEqual(initialHit, finalHit)) {\n                    let { context } = component;\n                    let arg = Object.assign(Object.assign({}, buildDatePointApiWithContext(initialHit.dateSpan, context)), { dayEl: initialHit.dayEl, jsEvent: ev.origEvent, view: context.viewApi || context.calendarApi.view });\n                    context.emitter.trigger('dateClick', arg);\n                }\n            }\n        };\n        // we DO want to watch pointer moves because otherwise finalHit won't get populated\n        this.dragging = new FeaturefulElementDragging(settings.el);\n        this.dragging.autoScroller.isEnabled = false;\n        let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings));\n        hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n        hitDragging.emitter.on('dragend', this.handleDragEnd);\n    }\n    destroy() {\n        this.dragging.destroy();\n    }\n}\n\n/*\nTracks when the user selects a portion of time of a component,\nconstituted by a drag over date cells, with a possible delay at the beginning of the drag.\n*/\nclass DateSelecting extends Interaction {\n    constructor(settings) {\n        super(settings);\n        this.dragSelection = null;\n        this.handlePointerDown = (ev) => {\n            let { component, dragging } = this;\n            let { options } = component.context;\n            let canSelect = options.selectable &&\n                component.isValidDateDownEl(ev.origEvent.target);\n            // don't bother to watch expensive moves if component won't do selection\n            dragging.setIgnoreMove(!canSelect);\n            // if touch, require user to hold down\n            dragging.delay = ev.isTouch ? getComponentTouchDelay$1(component) : null;\n        };\n        this.handleDragStart = (ev) => {\n            this.component.context.calendarApi.unselect(ev); // unselect previous selections\n        };\n        this.handleHitUpdate = (hit, isFinal) => {\n            let { context } = this.component;\n            let dragSelection = null;\n            let isInvalid = false;\n            if (hit) {\n                let initialHit = this.hitDragging.initialHit;\n                let disallowed = hit.componentId === initialHit.componentId\n                    && this.isHitComboAllowed\n                    && !this.isHitComboAllowed(initialHit, hit);\n                if (!disallowed) {\n                    dragSelection = joinHitsIntoSelection(initialHit, hit, context.pluginHooks.dateSelectionTransformers);\n                }\n                if (!dragSelection || !isDateSelectionValid(dragSelection, hit.dateProfile, context)) {\n                    isInvalid = true;\n                    dragSelection = null;\n                }\n            }\n            if (dragSelection) {\n                context.dispatch({ type: 'SELECT_DATES', selection: dragSelection });\n            }\n            else if (!isFinal) { // only unselect if moved away while dragging\n                context.dispatch({ type: 'UNSELECT_DATES' });\n            }\n            if (!isInvalid) {\n                enableCursor();\n            }\n            else {\n                disableCursor();\n            }\n            if (!isFinal) {\n                this.dragSelection = dragSelection; // only clear if moved away from all hits while dragging\n            }\n        };\n        this.handlePointerUp = (pev) => {\n            if (this.dragSelection) {\n                // selection is already rendered, so just need to report selection\n                triggerDateSelect(this.dragSelection, pev, this.component.context);\n                this.dragSelection = null;\n            }\n        };\n        let { component } = settings;\n        let { options } = component.context;\n        let dragging = this.dragging = new FeaturefulElementDragging(settings.el);\n        dragging.touchScrollAllowed = false;\n        dragging.minDistance = options.selectMinDistance || 0;\n        dragging.autoScroller.isEnabled = options.dragScroll;\n        let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings));\n        hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n        hitDragging.emitter.on('dragstart', this.handleDragStart);\n        hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n        hitDragging.emitter.on('pointerup', this.handlePointerUp);\n    }\n    destroy() {\n        this.dragging.destroy();\n    }\n}\nfunction getComponentTouchDelay$1(component) {\n    let { options } = component.context;\n    let delay = options.selectLongPressDelay;\n    if (delay == null) {\n        delay = options.longPressDelay;\n    }\n    return delay;\n}\nfunction joinHitsIntoSelection(hit0, hit1, dateSelectionTransformers) {\n    let dateSpan0 = hit0.dateSpan;\n    let dateSpan1 = hit1.dateSpan;\n    let ms = [\n        dateSpan0.range.start,\n        dateSpan0.range.end,\n        dateSpan1.range.start,\n        dateSpan1.range.end,\n    ];\n    ms.sort(compareNumbers);\n    let props = {};\n    for (let transformer of dateSelectionTransformers) {\n        let res = transformer(hit0, hit1);\n        if (res === false) {\n            return null;\n        }\n        if (res) {\n            Object.assign(props, res);\n        }\n    }\n    props.range = { start: ms[0], end: ms[3] };\n    props.allDay = dateSpan0.allDay;\n    return props;\n}\n\nclass EventDragging extends Interaction {\n    constructor(settings) {\n        super(settings);\n        // internal state\n        this.subjectEl = null;\n        this.subjectSeg = null; // the seg being selected/dragged\n        this.isDragging = false;\n        this.eventRange = null;\n        this.relevantEvents = null; // the events being dragged\n        this.receivingContext = null;\n        this.validMutation = null;\n        this.mutatedRelevantEvents = null;\n        this.handlePointerDown = (ev) => {\n            let origTarget = ev.origEvent.target;\n            let { component, dragging } = this;\n            let { mirror } = dragging;\n            let { options } = component.context;\n            let initialContext = component.context;\n            this.subjectEl = ev.subjectEl;\n            let subjectSeg = this.subjectSeg = getElSeg(ev.subjectEl);\n            let eventRange = this.eventRange = subjectSeg.eventRange;\n            let eventInstanceId = eventRange.instance.instanceId;\n            this.relevantEvents = getRelevantEvents(initialContext.getCurrentData().eventStore, eventInstanceId);\n            dragging.minDistance = ev.isTouch ? 0 : options.eventDragMinDistance;\n            dragging.delay =\n                // only do a touch delay if touch and this event hasn't been selected yet\n                (ev.isTouch && eventInstanceId !== component.props.eventSelection) ?\n                    getComponentTouchDelay(component) :\n                    null;\n            if (options.fixedMirrorParent) {\n                mirror.parentNode = options.fixedMirrorParent;\n            }\n            else {\n                mirror.parentNode = elementClosest(origTarget, '.fc');\n            }\n            mirror.revertDuration = options.dragRevertDuration;\n            let isValid = component.isValidSegDownEl(origTarget) &&\n                !elementClosest(origTarget, '.fc-event-resizer'); // NOT on a resizer\n            dragging.setIgnoreMove(!isValid);\n            // disable dragging for elements that are resizable (ie, selectable)\n            // but are not draggable\n            this.isDragging = isValid &&\n                ev.subjectEl.classList.contains('fc-event-draggable');\n        };\n        this.handleDragStart = (ev) => {\n            let initialContext = this.component.context;\n            let eventRange = this.eventRange;\n            let eventInstanceId = eventRange.instance.instanceId;\n            if (ev.isTouch) {\n                // need to select a different event?\n                if (eventInstanceId !== this.component.props.eventSelection) {\n                    initialContext.dispatch({ type: 'SELECT_EVENT', eventInstanceId });\n                }\n            }\n            else {\n                // if now using mouse, but was previous touch interaction, clear selected event\n                initialContext.dispatch({ type: 'UNSELECT_EVENT' });\n            }\n            if (this.isDragging) {\n                initialContext.calendarApi.unselect(ev); // unselect *date* selection\n                initialContext.emitter.trigger('eventDragStart', {\n                    el: this.subjectEl,\n                    event: new EventImpl(initialContext, eventRange.def, eventRange.instance),\n                    jsEvent: ev.origEvent,\n                    view: initialContext.viewApi,\n                });\n            }\n        };\n        this.handleHitUpdate = (hit, isFinal) => {\n            if (!this.isDragging) {\n                return;\n            }\n            let relevantEvents = this.relevantEvents;\n            let initialHit = this.hitDragging.initialHit;\n            let initialContext = this.component.context;\n            // states based on new hit\n            let receivingContext = null;\n            let mutation = null;\n            let mutatedRelevantEvents = null;\n            let isInvalid = false;\n            let interaction = {\n                affectedEvents: relevantEvents,\n                mutatedEvents: createEmptyEventStore(),\n                isEvent: true,\n            };\n            if (hit) {\n                receivingContext = hit.context;\n                let receivingOptions = receivingContext.options;\n                if (initialContext === receivingContext ||\n                    (receivingOptions.editable && receivingOptions.droppable)) {\n                    mutation = computeEventMutation(initialHit, hit, this.eventRange.instance.range.start, receivingContext.getCurrentData().pluginHooks.eventDragMutationMassagers);\n                    if (mutation) {\n                        mutatedRelevantEvents = applyMutationToEventStore(relevantEvents, receivingContext.getCurrentData().eventUiBases, mutation, receivingContext);\n                        interaction.mutatedEvents = mutatedRelevantEvents;\n                        if (!isInteractionValid(interaction, hit.dateProfile, receivingContext)) {\n                            isInvalid = true;\n                            mutation = null;\n                            mutatedRelevantEvents = null;\n                            interaction.mutatedEvents = createEmptyEventStore();\n                        }\n                    }\n                }\n                else {\n                    receivingContext = null;\n                }\n            }\n            this.displayDrag(receivingContext, interaction);\n            if (!isInvalid) {\n                enableCursor();\n            }\n            else {\n                disableCursor();\n            }\n            if (!isFinal) {\n                if (initialContext === receivingContext && // TODO: write test for this\n                    isHitsEqual(initialHit, hit)) {\n                    mutation = null;\n                }\n                this.dragging.setMirrorNeedsRevert(!mutation);\n                // render the mirror if no already-rendered mirror\n                // TODO: wish we could somehow wait for dispatch to guarantee render\n                this.dragging.setMirrorIsVisible(!hit || !this.subjectEl.getRootNode().querySelector('.fc-event-mirror'));\n                // assign states based on new hit\n                this.receivingContext = receivingContext;\n                this.validMutation = mutation;\n                this.mutatedRelevantEvents = mutatedRelevantEvents;\n            }\n        };\n        this.handlePointerUp = () => {\n            if (!this.isDragging) {\n                this.cleanup(); // because handleDragEnd won't fire\n            }\n        };\n        this.handleDragEnd = (ev) => {\n            if (this.isDragging) {\n                let initialContext = this.component.context;\n                let initialView = initialContext.viewApi;\n                let { receivingContext, validMutation } = this;\n                let eventDef = this.eventRange.def;\n                let eventInstance = this.eventRange.instance;\n                let eventApi = new EventImpl(initialContext, eventDef, eventInstance);\n                let relevantEvents = this.relevantEvents;\n                let mutatedRelevantEvents = this.mutatedRelevantEvents;\n                let { finalHit } = this.hitDragging;\n                this.clearDrag(); // must happen after revert animation\n                initialContext.emitter.trigger('eventDragStop', {\n                    el: this.subjectEl,\n                    event: eventApi,\n                    jsEvent: ev.origEvent,\n                    view: initialView,\n                });\n                if (validMutation) {\n                    // dropped within same calendar\n                    if (receivingContext === initialContext) {\n                        let updatedEventApi = new EventImpl(initialContext, mutatedRelevantEvents.defs[eventDef.defId], eventInstance ? mutatedRelevantEvents.instances[eventInstance.instanceId] : null);\n                        initialContext.dispatch({\n                            type: 'MERGE_EVENTS',\n                            eventStore: mutatedRelevantEvents,\n                        });\n                        let eventChangeArg = {\n                            oldEvent: eventApi,\n                            event: updatedEventApi,\n                            relatedEvents: buildEventApis(mutatedRelevantEvents, initialContext, eventInstance),\n                            revert() {\n                                initialContext.dispatch({\n                                    type: 'MERGE_EVENTS',\n                                    eventStore: relevantEvents, // the pre-change data\n                                });\n                            },\n                        };\n                        let transformed = {};\n                        for (let transformer of initialContext.getCurrentData().pluginHooks.eventDropTransformers) {\n                            Object.assign(transformed, transformer(validMutation, initialContext));\n                        }\n                        initialContext.emitter.trigger('eventDrop', Object.assign(Object.assign(Object.assign({}, eventChangeArg), transformed), { el: ev.subjectEl, delta: validMutation.datesDelta, jsEvent: ev.origEvent, view: initialView }));\n                        initialContext.emitter.trigger('eventChange', eventChangeArg);\n                        // dropped in different calendar\n                    }\n                    else if (receivingContext) {\n                        let eventRemoveArg = {\n                            event: eventApi,\n                            relatedEvents: buildEventApis(relevantEvents, initialContext, eventInstance),\n                            revert() {\n                                initialContext.dispatch({\n                                    type: 'MERGE_EVENTS',\n                                    eventStore: relevantEvents,\n                                });\n                            },\n                        };\n                        initialContext.emitter.trigger('eventLeave', Object.assign(Object.assign({}, eventRemoveArg), { draggedEl: ev.subjectEl, view: initialView }));\n                        initialContext.dispatch({\n                            type: 'REMOVE_EVENTS',\n                            eventStore: relevantEvents,\n                        });\n                        initialContext.emitter.trigger('eventRemove', eventRemoveArg);\n                        let addedEventDef = mutatedRelevantEvents.defs[eventDef.defId];\n                        let addedEventInstance = mutatedRelevantEvents.instances[eventInstance.instanceId];\n                        let addedEventApi = new EventImpl(receivingContext, addedEventDef, addedEventInstance);\n                        receivingContext.dispatch({\n                            type: 'MERGE_EVENTS',\n                            eventStore: mutatedRelevantEvents,\n                        });\n                        let eventAddArg = {\n                            event: addedEventApi,\n                            relatedEvents: buildEventApis(mutatedRelevantEvents, receivingContext, addedEventInstance),\n                            revert() {\n                                receivingContext.dispatch({\n                                    type: 'REMOVE_EVENTS',\n                                    eventStore: mutatedRelevantEvents,\n                                });\n                            },\n                        };\n                        receivingContext.emitter.trigger('eventAdd', eventAddArg);\n                        if (ev.isTouch) {\n                            receivingContext.dispatch({\n                                type: 'SELECT_EVENT',\n                                eventInstanceId: eventInstance.instanceId,\n                            });\n                        }\n                        receivingContext.emitter.trigger('drop', Object.assign(Object.assign({}, buildDatePointApiWithContext(finalHit.dateSpan, receivingContext)), { draggedEl: ev.subjectEl, jsEvent: ev.origEvent, view: finalHit.context.viewApi }));\n                        receivingContext.emitter.trigger('eventReceive', Object.assign(Object.assign({}, eventAddArg), { draggedEl: ev.subjectEl, view: finalHit.context.viewApi }));\n                    }\n                }\n                else {\n                    initialContext.emitter.trigger('_noEventDrop');\n                }\n            }\n            this.cleanup();\n        };\n        let { component } = this;\n        let { options } = component.context;\n        let dragging = this.dragging = new FeaturefulElementDragging(settings.el);\n        dragging.pointer.selector = EventDragging.SELECTOR;\n        dragging.touchScrollAllowed = false;\n        dragging.autoScroller.isEnabled = options.dragScroll;\n        let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsStore);\n        hitDragging.useSubjectCenter = settings.useEventCenter;\n        hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n        hitDragging.emitter.on('dragstart', this.handleDragStart);\n        hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n        hitDragging.emitter.on('pointerup', this.handlePointerUp);\n        hitDragging.emitter.on('dragend', this.handleDragEnd);\n    }\n    destroy() {\n        this.dragging.destroy();\n    }\n    // render a drag state on the next receivingCalendar\n    displayDrag(nextContext, state) {\n        let initialContext = this.component.context;\n        let prevContext = this.receivingContext;\n        // does the previous calendar need to be cleared?\n        if (prevContext && prevContext !== nextContext) {\n            // does the initial calendar need to be cleared?\n            // if so, don't clear all the way. we still need to to hide the affectedEvents\n            if (prevContext === initialContext) {\n                prevContext.dispatch({\n                    type: 'SET_EVENT_DRAG',\n                    state: {\n                        affectedEvents: state.affectedEvents,\n                        mutatedEvents: createEmptyEventStore(),\n                        isEvent: true,\n                    },\n                });\n                // completely clear the old calendar if it wasn't the initial\n            }\n            else {\n                prevContext.dispatch({ type: 'UNSET_EVENT_DRAG' });\n            }\n        }\n        if (nextContext) {\n            nextContext.dispatch({ type: 'SET_EVENT_DRAG', state });\n        }\n    }\n    clearDrag() {\n        let initialCalendar = this.component.context;\n        let { receivingContext } = this;\n        if (receivingContext) {\n            receivingContext.dispatch({ type: 'UNSET_EVENT_DRAG' });\n        }\n        // the initial calendar might have an dummy drag state from displayDrag\n        if (initialCalendar !== receivingContext) {\n            initialCalendar.dispatch({ type: 'UNSET_EVENT_DRAG' });\n        }\n    }\n    cleanup() {\n        this.subjectSeg = null;\n        this.isDragging = false;\n        this.eventRange = null;\n        this.relevantEvents = null;\n        this.receivingContext = null;\n        this.validMutation = null;\n        this.mutatedRelevantEvents = null;\n    }\n}\n// TODO: test this in IE11\n// QUESTION: why do we need it on the resizable???\nEventDragging.SELECTOR = '.fc-event-draggable, .fc-event-resizable';\nfunction computeEventMutation(hit0, hit1, eventInstanceStart, massagers) {\n    let dateSpan0 = hit0.dateSpan;\n    let dateSpan1 = hit1.dateSpan;\n    let date0 = dateSpan0.range.start;\n    let date1 = dateSpan1.range.start;\n    let standardProps = {};\n    if (dateSpan0.allDay !== dateSpan1.allDay) {\n        standardProps.allDay = dateSpan1.allDay;\n        standardProps.hasEnd = hit1.context.options.allDayMaintainDuration;\n        if (dateSpan1.allDay) {\n            // means date1 is already start-of-day,\n            // but date0 needs to be converted\n            date0 = startOfDay(eventInstanceStart);\n        }\n        else {\n            // Moving from allDate->timed\n            // Doesn't matter where on the event the drag began, mutate the event's start-date to date1\n            date0 = eventInstanceStart;\n        }\n    }\n    let delta = diffDates(date0, date1, hit0.context.dateEnv, hit0.componentId === hit1.componentId ?\n        hit0.largeUnit :\n        null);\n    if (delta.milliseconds) { // has hours/minutes/seconds\n        standardProps.allDay = false;\n    }\n    let mutation = {\n        datesDelta: delta,\n        standardProps,\n    };\n    for (let massager of massagers) {\n        massager(mutation, hit0, hit1);\n    }\n    return mutation;\n}\nfunction getComponentTouchDelay(component) {\n    let { options } = component.context;\n    let delay = options.eventLongPressDelay;\n    if (delay == null) {\n        delay = options.longPressDelay;\n    }\n    return delay;\n}\n\nclass EventResizing extends Interaction {\n    constructor(settings) {\n        super(settings);\n        // internal state\n        this.draggingSegEl = null;\n        this.draggingSeg = null; // TODO: rename to resizingSeg? subjectSeg?\n        this.eventRange = null;\n        this.relevantEvents = null;\n        this.validMutation = null;\n        this.mutatedRelevantEvents = null;\n        this.handlePointerDown = (ev) => {\n            let { component } = this;\n            let segEl = this.querySegEl(ev);\n            let seg = getElSeg(segEl);\n            let eventRange = this.eventRange = seg.eventRange;\n            this.dragging.minDistance = component.context.options.eventDragMinDistance;\n            // if touch, need to be working with a selected event\n            this.dragging.setIgnoreMove(!this.component.isValidSegDownEl(ev.origEvent.target) ||\n                (ev.isTouch && this.component.props.eventSelection !== eventRange.instance.instanceId));\n        };\n        this.handleDragStart = (ev) => {\n            let { context } = this.component;\n            let eventRange = this.eventRange;\n            this.relevantEvents = getRelevantEvents(context.getCurrentData().eventStore, this.eventRange.instance.instanceId);\n            let segEl = this.querySegEl(ev);\n            this.draggingSegEl = segEl;\n            this.draggingSeg = getElSeg(segEl);\n            context.calendarApi.unselect();\n            context.emitter.trigger('eventResizeStart', {\n                el: segEl,\n                event: new EventImpl(context, eventRange.def, eventRange.instance),\n                jsEvent: ev.origEvent,\n                view: context.viewApi,\n            });\n        };\n        this.handleHitUpdate = (hit, isFinal, ev) => {\n            let { context } = this.component;\n            let relevantEvents = this.relevantEvents;\n            let initialHit = this.hitDragging.initialHit;\n            let eventInstance = this.eventRange.instance;\n            let mutation = null;\n            let mutatedRelevantEvents = null;\n            let isInvalid = false;\n            let interaction = {\n                affectedEvents: relevantEvents,\n                mutatedEvents: createEmptyEventStore(),\n                isEvent: true,\n            };\n            if (hit) {\n                let disallowed = hit.componentId === initialHit.componentId\n                    && this.isHitComboAllowed\n                    && !this.isHitComboAllowed(initialHit, hit);\n                if (!disallowed) {\n                    mutation = computeMutation(initialHit, hit, ev.subjectEl.classList.contains('fc-event-resizer-start'), eventInstance.range);\n                }\n            }\n            if (mutation) {\n                mutatedRelevantEvents = applyMutationToEventStore(relevantEvents, context.getCurrentData().eventUiBases, mutation, context);\n                interaction.mutatedEvents = mutatedRelevantEvents;\n                if (!isInteractionValid(interaction, hit.dateProfile, context)) {\n                    isInvalid = true;\n                    mutation = null;\n                    mutatedRelevantEvents = null;\n                    interaction.mutatedEvents = null;\n                }\n            }\n            if (mutatedRelevantEvents) {\n                context.dispatch({\n                    type: 'SET_EVENT_RESIZE',\n                    state: interaction,\n                });\n            }\n            else {\n                context.dispatch({ type: 'UNSET_EVENT_RESIZE' });\n            }\n            if (!isInvalid) {\n                enableCursor();\n            }\n            else {\n                disableCursor();\n            }\n            if (!isFinal) {\n                if (mutation && isHitsEqual(initialHit, hit)) {\n                    mutation = null;\n                }\n                this.validMutation = mutation;\n                this.mutatedRelevantEvents = mutatedRelevantEvents;\n            }\n        };\n        this.handleDragEnd = (ev) => {\n            let { context } = this.component;\n            let eventDef = this.eventRange.def;\n            let eventInstance = this.eventRange.instance;\n            let eventApi = new EventImpl(context, eventDef, eventInstance);\n            let relevantEvents = this.relevantEvents;\n            let mutatedRelevantEvents = this.mutatedRelevantEvents;\n            context.emitter.trigger('eventResizeStop', {\n                el: this.draggingSegEl,\n                event: eventApi,\n                jsEvent: ev.origEvent,\n                view: context.viewApi,\n            });\n            if (this.validMutation) {\n                let updatedEventApi = new EventImpl(context, mutatedRelevantEvents.defs[eventDef.defId], eventInstance ? mutatedRelevantEvents.instances[eventInstance.instanceId] : null);\n                context.dispatch({\n                    type: 'MERGE_EVENTS',\n                    eventStore: mutatedRelevantEvents,\n                });\n                let eventChangeArg = {\n                    oldEvent: eventApi,\n                    event: updatedEventApi,\n                    relatedEvents: buildEventApis(mutatedRelevantEvents, context, eventInstance),\n                    revert() {\n                        context.dispatch({\n                            type: 'MERGE_EVENTS',\n                            eventStore: relevantEvents, // the pre-change events\n                        });\n                    },\n                };\n                context.emitter.trigger('eventResize', Object.assign(Object.assign({}, eventChangeArg), { el: this.draggingSegEl, startDelta: this.validMutation.startDelta || createDuration(0), endDelta: this.validMutation.endDelta || createDuration(0), jsEvent: ev.origEvent, view: context.viewApi }));\n                context.emitter.trigger('eventChange', eventChangeArg);\n            }\n            else {\n                context.emitter.trigger('_noEventResize');\n            }\n            // reset all internal state\n            this.draggingSeg = null;\n            this.relevantEvents = null;\n            this.validMutation = null;\n            // okay to keep eventInstance around. useful to set it in handlePointerDown\n        };\n        let { component } = settings;\n        let dragging = this.dragging = new FeaturefulElementDragging(settings.el);\n        dragging.pointer.selector = '.fc-event-resizer';\n        dragging.touchScrollAllowed = false;\n        dragging.autoScroller.isEnabled = component.context.options.dragScroll;\n        let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings));\n        hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n        hitDragging.emitter.on('dragstart', this.handleDragStart);\n        hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n        hitDragging.emitter.on('dragend', this.handleDragEnd);\n    }\n    destroy() {\n        this.dragging.destroy();\n    }\n    querySegEl(ev) {\n        return elementClosest(ev.subjectEl, '.fc-event');\n    }\n}\nfunction computeMutation(hit0, hit1, isFromStart, instanceRange) {\n    let dateEnv = hit0.context.dateEnv;\n    let date0 = hit0.dateSpan.range.start;\n    let date1 = hit1.dateSpan.range.start;\n    let delta = diffDates(date0, date1, dateEnv, hit0.largeUnit);\n    if (isFromStart) {\n        if (dateEnv.add(instanceRange.start, delta) < instanceRange.end) {\n            return { startDelta: delta };\n        }\n    }\n    else if (dateEnv.add(instanceRange.end, delta) > instanceRange.start) {\n        return { endDelta: delta };\n    }\n    return null;\n}\n\nclass UnselectAuto {\n    constructor(context) {\n        this.context = context;\n        this.isRecentPointerDateSelect = false; // wish we could use a selector to detect date selection, but uses hit system\n        this.matchesCancel = false;\n        this.matchesEvent = false;\n        this.onSelect = (selectInfo) => {\n            if (selectInfo.jsEvent) {\n                this.isRecentPointerDateSelect = true;\n            }\n        };\n        this.onDocumentPointerDown = (pev) => {\n            let unselectCancel = this.context.options.unselectCancel;\n            let downEl = getEventTargetViaRoot(pev.origEvent);\n            this.matchesCancel = !!elementClosest(downEl, unselectCancel);\n            this.matchesEvent = !!elementClosest(downEl, EventDragging.SELECTOR); // interaction started on an event?\n        };\n        this.onDocumentPointerUp = (pev) => {\n            let { context } = this;\n            let { documentPointer } = this;\n            let calendarState = context.getCurrentData();\n            // touch-scrolling should never unfocus any type of selection\n            if (!documentPointer.wasTouchScroll) {\n                if (calendarState.dateSelection && // an existing date selection?\n                    !this.isRecentPointerDateSelect // a new pointer-initiated date selection since last onDocumentPointerUp?\n                ) {\n                    let unselectAuto = context.options.unselectAuto;\n                    if (unselectAuto && (!unselectAuto || !this.matchesCancel)) {\n                        context.calendarApi.unselect(pev);\n                    }\n                }\n                if (calendarState.eventSelection && // an existing event selected?\n                    !this.matchesEvent // interaction DIDN'T start on an event\n                ) {\n                    context.dispatch({ type: 'UNSELECT_EVENT' });\n                }\n            }\n            this.isRecentPointerDateSelect = false;\n        };\n        let documentPointer = this.documentPointer = new PointerDragging(document);\n        documentPointer.shouldIgnoreMove = true;\n        documentPointer.shouldWatchScroll = false;\n        documentPointer.emitter.on('pointerdown', this.onDocumentPointerDown);\n        documentPointer.emitter.on('pointerup', this.onDocumentPointerUp);\n        /*\n        TODO: better way to know about whether there was a selection with the pointer\n        */\n        context.emitter.on('select', this.onSelect);\n    }\n    destroy() {\n        this.context.emitter.off('select', this.onSelect);\n        this.documentPointer.destroy();\n    }\n}\n\nconst OPTION_REFINERS = {\n    fixedMirrorParent: identity,\n};\nconst LISTENER_REFINERS = {\n    dateClick: identity,\n    eventDragStart: identity,\n    eventDragStop: identity,\n    eventDrop: identity,\n    eventResizeStart: identity,\n    eventResizeStop: identity,\n    eventResize: identity,\n    drop: identity,\n    eventReceive: identity,\n    eventLeave: identity,\n};\n\n/*\nGiven an already instantiated draggable object for one-or-more elements,\nInterprets any dragging as an attempt to drag an events that lives outside\nof a calendar onto a calendar.\n*/\nclass ExternalElementDragging {\n    constructor(dragging, suppliedDragMeta) {\n        this.receivingContext = null;\n        this.droppableEvent = null; // will exist for all drags, even if create:false\n        this.suppliedDragMeta = null;\n        this.dragMeta = null;\n        this.handleDragStart = (ev) => {\n            this.dragMeta = this.buildDragMeta(ev.subjectEl);\n        };\n        this.handleHitUpdate = (hit, isFinal, ev) => {\n            let { dragging } = this.hitDragging;\n            let receivingContext = null;\n            let droppableEvent = null;\n            let isInvalid = false;\n            let interaction = {\n                affectedEvents: createEmptyEventStore(),\n                mutatedEvents: createEmptyEventStore(),\n                isEvent: this.dragMeta.create,\n            };\n            if (hit) {\n                receivingContext = hit.context;\n                if (this.canDropElOnCalendar(ev.subjectEl, receivingContext)) {\n                    droppableEvent = computeEventForDateSpan(hit.dateSpan, this.dragMeta, receivingContext);\n                    interaction.mutatedEvents = eventTupleToStore(droppableEvent);\n                    isInvalid = !isInteractionValid(interaction, hit.dateProfile, receivingContext);\n                    if (isInvalid) {\n                        interaction.mutatedEvents = createEmptyEventStore();\n                        droppableEvent = null;\n                    }\n                }\n            }\n            this.displayDrag(receivingContext, interaction);\n            // show mirror if no already-rendered mirror element OR if we are shutting down the mirror (?)\n            // TODO: wish we could somehow wait for dispatch to guarantee render\n            dragging.setMirrorIsVisible(isFinal || !droppableEvent || !document.querySelector('.fc-event-mirror'));\n            if (!isInvalid) {\n                enableCursor();\n            }\n            else {\n                disableCursor();\n            }\n            if (!isFinal) {\n                dragging.setMirrorNeedsRevert(!droppableEvent);\n                this.receivingContext = receivingContext;\n                this.droppableEvent = droppableEvent;\n            }\n        };\n        this.handleDragEnd = (pev) => {\n            let { receivingContext, droppableEvent } = this;\n            this.clearDrag();\n            if (receivingContext && droppableEvent) {\n                let finalHit = this.hitDragging.finalHit;\n                let finalView = finalHit.context.viewApi;\n                let dragMeta = this.dragMeta;\n                receivingContext.emitter.trigger('drop', Object.assign(Object.assign({}, buildDatePointApiWithContext(finalHit.dateSpan, receivingContext)), { draggedEl: pev.subjectEl, jsEvent: pev.origEvent, view: finalView }));\n                if (dragMeta.create) {\n                    let addingEvents = eventTupleToStore(droppableEvent);\n                    receivingContext.dispatch({\n                        type: 'MERGE_EVENTS',\n                        eventStore: addingEvents,\n                    });\n                    if (pev.isTouch) {\n                        receivingContext.dispatch({\n                            type: 'SELECT_EVENT',\n                            eventInstanceId: droppableEvent.instance.instanceId,\n                        });\n                    }\n                    // signal that an external event landed\n                    receivingContext.emitter.trigger('eventReceive', {\n                        event: new EventImpl(receivingContext, droppableEvent.def, droppableEvent.instance),\n                        relatedEvents: [],\n                        revert() {\n                            receivingContext.dispatch({\n                                type: 'REMOVE_EVENTS',\n                                eventStore: addingEvents,\n                            });\n                        },\n                        draggedEl: pev.subjectEl,\n                        view: finalView,\n                    });\n                }\n            }\n            this.receivingContext = null;\n            this.droppableEvent = null;\n        };\n        let hitDragging = this.hitDragging = new HitDragging(dragging, interactionSettingsStore);\n        hitDragging.requireInitial = false; // will start outside of a component\n        hitDragging.emitter.on('dragstart', this.handleDragStart);\n        hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n        hitDragging.emitter.on('dragend', this.handleDragEnd);\n        this.suppliedDragMeta = suppliedDragMeta;\n    }\n    buildDragMeta(subjectEl) {\n        if (typeof this.suppliedDragMeta === 'object') {\n            return parseDragMeta(this.suppliedDragMeta);\n        }\n        if (typeof this.suppliedDragMeta === 'function') {\n            return parseDragMeta(this.suppliedDragMeta(subjectEl));\n        }\n        return getDragMetaFromEl(subjectEl);\n    }\n    displayDrag(nextContext, state) {\n        let prevContext = this.receivingContext;\n        if (prevContext && prevContext !== nextContext) {\n            prevContext.dispatch({ type: 'UNSET_EVENT_DRAG' });\n        }\n        if (nextContext) {\n            nextContext.dispatch({ type: 'SET_EVENT_DRAG', state });\n        }\n    }\n    clearDrag() {\n        if (this.receivingContext) {\n            this.receivingContext.dispatch({ type: 'UNSET_EVENT_DRAG' });\n        }\n    }\n    canDropElOnCalendar(el, receivingContext) {\n        let dropAccept = receivingContext.options.dropAccept;\n        if (typeof dropAccept === 'function') {\n            return dropAccept.call(receivingContext.calendarApi, el);\n        }\n        if (typeof dropAccept === 'string' && dropAccept) {\n            return Boolean(elementMatches(el, dropAccept));\n        }\n        return true;\n    }\n}\n// Utils for computing event store from the DragMeta\n// ----------------------------------------------------------------------------------------------------\nfunction computeEventForDateSpan(dateSpan, dragMeta, context) {\n    let defProps = Object.assign({}, dragMeta.leftoverProps);\n    for (let transform of context.pluginHooks.externalDefTransforms) {\n        Object.assign(defProps, transform(dateSpan, dragMeta));\n    }\n    let { refined, extra } = refineEventDef(defProps, context);\n    let def = parseEventDef(refined, extra, dragMeta.sourceId, dateSpan.allDay, context.options.forceEventDuration || Boolean(dragMeta.duration), // hasEnd\n    context);\n    let start = dateSpan.range.start;\n    // only rely on time info if drop zone is all-day,\n    // otherwise, we already know the time\n    if (dateSpan.allDay && dragMeta.startTime) {\n        start = context.dateEnv.add(start, dragMeta.startTime);\n    }\n    let end = dragMeta.duration ?\n        context.dateEnv.add(start, dragMeta.duration) :\n        getDefaultEventEnd(dateSpan.allDay, start, context);\n    let instance = createEventInstance(def.defId, { start, end });\n    return { def, instance };\n}\n// Utils for extracting data from element\n// ----------------------------------------------------------------------------------------------------\nfunction getDragMetaFromEl(el) {\n    let str = getEmbeddedElData(el, 'event');\n    let obj = str ?\n        JSON.parse(str) :\n        { create: false }; // if no embedded data, assume no event creation\n    return parseDragMeta(obj);\n}\nconfig.dataAttrPrefix = '';\nfunction getEmbeddedElData(el, name) {\n    let prefix = config.dataAttrPrefix;\n    let prefixedName = (prefix ? prefix + '-' : '') + name;\n    return el.getAttribute('data-' + prefixedName) || '';\n}\n\n/*\nMakes an element (that is *external* to any calendar) draggable.\nCan pass in data that determines how an event will be created when dropped onto a calendar.\nLeverages FullCalendar's internal drag-n-drop functionality WITHOUT a third-party drag system.\n*/\nclass ExternalDraggable {\n    constructor(el, settings = {}) {\n        this.handlePointerDown = (ev) => {\n            let { dragging } = this;\n            let { minDistance, longPressDelay } = this.settings;\n            dragging.minDistance =\n                minDistance != null ?\n                    minDistance :\n                    (ev.isTouch ? 0 : BASE_OPTION_DEFAULTS.eventDragMinDistance);\n            dragging.delay =\n                ev.isTouch ? // TODO: eventually read eventLongPressDelay instead vvv\n                    (longPressDelay != null ? longPressDelay : BASE_OPTION_DEFAULTS.longPressDelay) :\n                    0;\n        };\n        this.handleDragStart = (ev) => {\n            if (ev.isTouch &&\n                this.dragging.delay &&\n                ev.subjectEl.classList.contains('fc-event')) {\n                this.dragging.mirror.getMirrorEl().classList.add('fc-event-selected');\n            }\n        };\n        this.settings = settings;\n        let dragging = this.dragging = new FeaturefulElementDragging(el);\n        dragging.touchScrollAllowed = false;\n        if (settings.itemSelector != null) {\n            dragging.pointer.selector = settings.itemSelector;\n        }\n        if (settings.appendTo != null) {\n            dragging.mirror.parentNode = settings.appendTo; // TODO: write tests\n        }\n        dragging.emitter.on('pointerdown', this.handlePointerDown);\n        dragging.emitter.on('dragstart', this.handleDragStart);\n        new ExternalElementDragging(dragging, settings.eventData); // eslint-disable-line no-new\n    }\n    destroy() {\n        this.dragging.destroy();\n    }\n}\n\n/*\nDetects when a *THIRD-PARTY* drag-n-drop system interacts with elements.\nThe third-party system is responsible for drawing the visuals effects of the drag.\nThis class simply monitors for pointer movements and fires events.\nIt also has the ability to hide the moving element (the \"mirror\") during the drag.\n*/\nclass InferredElementDragging extends ElementDragging {\n    constructor(containerEl) {\n        super(containerEl);\n        this.shouldIgnoreMove = false;\n        this.mirrorSelector = '';\n        this.currentMirrorEl = null;\n        this.handlePointerDown = (ev) => {\n            this.emitter.trigger('pointerdown', ev);\n            if (!this.shouldIgnoreMove) {\n                // fire dragstart right away. does not support delay or min-distance\n                this.emitter.trigger('dragstart', ev);\n            }\n        };\n        this.handlePointerMove = (ev) => {\n            if (!this.shouldIgnoreMove) {\n                this.emitter.trigger('dragmove', ev);\n            }\n        };\n        this.handlePointerUp = (ev) => {\n            this.emitter.trigger('pointerup', ev);\n            if (!this.shouldIgnoreMove) {\n                // fire dragend right away. does not support a revert animation\n                this.emitter.trigger('dragend', ev);\n            }\n        };\n        let pointer = this.pointer = new PointerDragging(containerEl);\n        pointer.emitter.on('pointerdown', this.handlePointerDown);\n        pointer.emitter.on('pointermove', this.handlePointerMove);\n        pointer.emitter.on('pointerup', this.handlePointerUp);\n    }\n    destroy() {\n        this.pointer.destroy();\n    }\n    setIgnoreMove(bool) {\n        this.shouldIgnoreMove = bool;\n    }\n    setMirrorIsVisible(bool) {\n        if (bool) {\n            // restore a previously hidden element.\n            // use the reference in case the selector class has already been removed.\n            if (this.currentMirrorEl) {\n                this.currentMirrorEl.style.visibility = '';\n                this.currentMirrorEl = null;\n            }\n        }\n        else {\n            let mirrorEl = this.mirrorSelector\n                // TODO: somehow query FullCalendars WITHIN shadow-roots\n                ? document.querySelector(this.mirrorSelector)\n                : null;\n            if (mirrorEl) {\n                this.currentMirrorEl = mirrorEl;\n                mirrorEl.style.visibility = 'hidden';\n            }\n        }\n    }\n}\n\n/*\nBridges third-party drag-n-drop systems with FullCalendar.\nMust be instantiated and destroyed by caller.\n*/\nclass ThirdPartyDraggable {\n    constructor(containerOrSettings, settings) {\n        let containerEl = document;\n        if (\n        // wish we could just test instanceof EventTarget, but doesn't work in IE11\n        containerOrSettings === document ||\n            containerOrSettings instanceof Element) {\n            containerEl = containerOrSettings;\n            settings = settings || {};\n        }\n        else {\n            settings = (containerOrSettings || {});\n        }\n        let dragging = this.dragging = new InferredElementDragging(containerEl);\n        if (typeof settings.itemSelector === 'string') {\n            dragging.pointer.selector = settings.itemSelector;\n        }\n        else if (containerEl === document) {\n            dragging.pointer.selector = '[data-event]';\n        }\n        if (typeof settings.mirrorSelector === 'string') {\n            dragging.mirrorSelector = settings.mirrorSelector;\n        }\n        let externalDragging = new ExternalElementDragging(dragging, settings.eventData);\n        // The hit-detection system requires that the dnd-mirror-element be pointer-events:none,\n        // but this can't be guaranteed for third-party draggables, so disable\n        externalDragging.hitDragging.disablePointCheck = true;\n    }\n    destroy() {\n        this.dragging.destroy();\n    }\n}\n\nvar index = createPlugin({\n    name: '@fullcalendar/interaction',\n    componentInteractions: [DateClicking, DateSelecting, EventDragging, EventResizing],\n    calendarInteractions: [UnselectAuto],\n    elementDraggingImpl: FeaturefulElementDragging,\n    optionRefiners: OPTION_REFINERS,\n    listenerRefiners: LISTENER_REFINERS,\n});\n\nexport { ExternalDraggable as Draggable, ThirdPartyDraggable, index as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,OAAO,uBAAuB;AAC9B,IAAI,mBAAmB;AACvB,IAAI,cAAc;AAClB,IAAI,6BAA6B;AAcjC,IAAM,kBAAN,MAAsB;AAAA,EAClB,YAAY,aAAa;AACrB,SAAK,YAAY;AAEjB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB;AACxB,SAAK,oBAAoB;AAEzB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AAGtB,SAAK,kBAAkB,CAAC,OAAO;AAC3B,UAAI,CAAC,KAAK,kBAAkB,KACxB,qBAAqB,EAAE,KACvB,KAAK,SAAS,EAAE,GAAG;AACnB,YAAI,MAAM,KAAK,qBAAqB,IAAI,IAAI;AAC5C,aAAK,QAAQ,QAAQ,eAAe,GAAG;AACvC,aAAK,gBAAgB,GAAG;AACxB,YAAI,CAAC,KAAK,kBAAkB;AACxB,mBAAS,iBAAiB,aAAa,KAAK,eAAe;AAAA,QAC/D;AACA,iBAAS,iBAAiB,WAAW,KAAK,aAAa;AAAA,MAC3D;AAAA,IACJ;AACA,SAAK,kBAAkB,CAAC,OAAO;AAC3B,UAAI,MAAM,KAAK,qBAAqB,EAAE;AACtC,WAAK,aAAa,GAAG;AACrB,WAAK,QAAQ,QAAQ,eAAe,GAAG;AAAA,IAC3C;AACA,SAAK,gBAAgB,CAAC,OAAO;AACzB,eAAS,oBAAoB,aAAa,KAAK,eAAe;AAC9D,eAAS,oBAAoB,WAAW,KAAK,aAAa;AAC1D,WAAK,QAAQ,QAAQ,aAAa,KAAK,qBAAqB,EAAE,CAAC;AAC/D,WAAK,QAAQ;AAAA,IACjB;AAGA,SAAK,mBAAmB,CAAC,OAAO;AAC5B,UAAI,KAAK,SAAS,EAAE,GAAG;AACnB,aAAK,kBAAkB;AACvB,YAAI,MAAM,KAAK,qBAAqB,IAAI,IAAI;AAC5C,aAAK,QAAQ,QAAQ,eAAe,GAAG;AACvC,aAAK,gBAAgB,GAAG;AAGxB,YAAI,WAAW,GAAG;AAClB,YAAI,CAAC,KAAK,kBAAkB;AACxB,mBAAS,iBAAiB,aAAa,KAAK,eAAe;AAAA,QAC/D;AACA,iBAAS,iBAAiB,YAAY,KAAK,cAAc;AACzD,iBAAS,iBAAiB,eAAe,KAAK,cAAc;AAI5D,eAAO,iBAAiB,UAAU,KAAK,mBAAmB,IAAI;AAAA,MAClE;AAAA,IACJ;AACA,SAAK,kBAAkB,CAAC,OAAO;AAC3B,UAAI,MAAM,KAAK,qBAAqB,EAAE;AACtC,WAAK,aAAa,GAAG;AACrB,WAAK,QAAQ,QAAQ,eAAe,GAAG;AAAA,IAC3C;AACA,SAAK,iBAAiB,CAAC,OAAO;AAC1B,UAAI,KAAK,YAAY;AACjB,YAAI,WAAW,GAAG;AAClB,iBAAS,oBAAoB,aAAa,KAAK,eAAe;AAC9D,iBAAS,oBAAoB,YAAY,KAAK,cAAc;AAC5D,iBAAS,oBAAoB,eAAe,KAAK,cAAc;AAC/D,eAAO,oBAAoB,UAAU,KAAK,mBAAmB,IAAI;AACjE,aAAK,QAAQ,QAAQ,aAAa,KAAK,qBAAqB,EAAE,CAAC;AAC/D,aAAK,QAAQ;AACb,aAAK,kBAAkB;AACvB,2BAAmB;AAAA,MACvB;AAAA,IACJ;AACA,SAAK,oBAAoB,MAAM;AAC3B,WAAK,iBAAiB;AAAA,IAC1B;AACA,SAAK,eAAe,CAAC,OAAO;AACxB,UAAI,CAAC,KAAK,kBAAkB;AACxB,YAAI,QAAS,OAAO,UAAU,KAAK,cAAe,KAAK;AACvD,YAAI,QAAS,OAAO,UAAU,KAAK,cAAe,KAAK;AACvD,aAAK,QAAQ,QAAQ,eAAe;AAAA,UAChC,WAAW;AAAA,UACX,SAAS,KAAK;AAAA,UACd,WAAW,KAAK;AAAA,UAChB;AAAA,UACA;AAAA,UACA,QAAQ,QAAQ,KAAK;AAAA,UACrB,QAAQ,QAAQ,KAAK;AAAA,QACzB,CAAC;AAAA,MACL;AAAA,IACJ;AACA,SAAK,cAAc;AACnB,SAAK,UAAU,IAAI,QAAQ;AAC3B,gBAAY,iBAAiB,aAAa,KAAK,eAAe;AAC9D,gBAAY,iBAAiB,cAAc,KAAK,kBAAkB,EAAE,SAAS,KAAK,CAAC;AACnF,oBAAgB;AAAA,EACpB;AAAA,EACA,UAAU;AACN,SAAK,YAAY,oBAAoB,aAAa,KAAK,eAAe;AACtE,SAAK,YAAY,oBAAoB,cAAc,KAAK,kBAAkB,EAAE,SAAS,KAAK,CAAC;AAC3F,sBAAkB;AAAA,EACtB;AAAA,EACA,SAAS,IAAI;AACT,QAAI,YAAY,KAAK,eAAe,EAAE;AACtC,QAAI,SAAS,GAAG;AAChB,QAAI,cACC,CAAC,KAAK,kBAAkB,eAAe,QAAQ,KAAK,cAAc,IAAI;AACvE,WAAK,YAAY;AACjB,WAAK,aAAa;AAClB,WAAK,iBAAiB;AACtB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,UAAU;AACN,iCAA6B;AAC7B,SAAK,aAAa;AAClB,SAAK,YAAY;AAEjB,SAAK,mBAAmB;AAAA,EAC5B;AAAA,EACA,eAAe,IAAI;AACf,QAAI,KAAK,UAAU;AACf,aAAO,eAAe,GAAG,QAAQ,KAAK,QAAQ;AAAA,IAClD;AACA,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,oBAAoB;AAChB,WAAO,oBAAoB,KAAK;AAAA,EACpC;AAAA;AAAA,EAEA,oBAAoB;AAChB,QAAI,KAAK,YAAY;AACjB,mCAA6B;AAAA,IACjC;AAAA,EACJ;AAAA;AAAA;AAAA,EAGA,gBAAgB,IAAI;AAChB,QAAI,KAAK,mBAAmB;AACxB,WAAK,aAAa,EAAE;AACpB,aAAO,iBAAiB,UAAU,KAAK,cAAc,IAAI;AAAA,IAC7D;AAAA,EACJ;AAAA,EACA,aAAa,IAAI;AACb,QAAI,KAAK,mBAAmB;AACxB,WAAK,YAAY,GAAG;AACpB,WAAK,YAAY,GAAG;AACpB,WAAK,cAAc,OAAO;AAC1B,WAAK,cAAc,OAAO;AAAA,IAC9B;AAAA,EACJ;AAAA,EACA,qBAAqB;AACjB,QAAI,KAAK,mBAAmB;AACxB,aAAO,oBAAoB,UAAU,KAAK,cAAc,IAAI;AAAA,IAChE;AAAA,EACJ;AAAA;AAAA;AAAA,EAGA,qBAAqB,IAAI,SAAS;AAC9B,QAAI,SAAS;AACb,QAAI,SAAS;AAEb,QAAI,SAAS;AACT,WAAK,YAAY,GAAG;AACpB,WAAK,YAAY,GAAG;AAAA,IACxB,OACK;AACD,eAAS,GAAG,QAAQ,KAAK;AACzB,eAAS,GAAG,QAAQ,KAAK;AAAA,IAC7B;AACA,WAAO;AAAA,MACH,WAAW;AAAA,MACX,SAAS;AAAA,MACT,WAAW,KAAK;AAAA,MAChB,OAAO,GAAG;AAAA,MACV,OAAO,GAAG;AAAA,MACV;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,qBAAqB,IAAI,SAAS;AAC9B,QAAI,UAAU,GAAG;AACjB,QAAI;AACJ,QAAI;AACJ,QAAI,SAAS;AACb,QAAI,SAAS;AAGb,QAAI,WAAW,QAAQ,QAAQ;AAC3B,cAAQ,QAAQ,CAAC,EAAE;AACnB,cAAQ,QAAQ,CAAC,EAAE;AAAA,IACvB,OACK;AACD,cAAQ,GAAG;AACX,cAAQ,GAAG;AAAA,IACf;AAEA,QAAI,SAAS;AACT,WAAK,YAAY;AACjB,WAAK,YAAY;AAAA,IACrB,OACK;AACD,eAAS,QAAQ,KAAK;AACtB,eAAS,QAAQ,KAAK;AAAA,IAC1B;AACA,WAAO;AAAA,MACH,WAAW;AAAA,MACX,SAAS;AAAA,MACT,WAAW,KAAK;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,SAAS,qBAAqB,IAAI;AAC9B,SAAO,GAAG,WAAW,KAAK,CAAC,GAAG;AAClC;AAGA,SAAS,qBAAqB;AAC1B,sBAAoB;AACpB,aAAW,MAAM;AACb,wBAAoB;AAAA,EACxB,GAAG,OAAO,oBAAoB;AAClC;AAGA,SAAS,kBAAkB;AACvB,iBAAe;AACf,MAAI,gBAAgB,GAAG;AACnB,WAAO,iBAAiB,aAAa,mBAAmB,EAAE,SAAS,MAAM,CAAC;AAAA,EAC9E;AACJ;AACA,SAAS,oBAAoB;AACzB,iBAAe;AACf,MAAI,CAAC,aAAa;AACd,WAAO,oBAAoB,aAAa,mBAAmB,EAAE,SAAS,MAAM,CAAC;AAAA,EACjF;AACJ;AACA,SAAS,kBAAkB,IAAI;AAC3B,MAAI,4BAA4B;AAC5B,OAAG,eAAe;AAAA,EACtB;AACJ;AAOA,IAAM,gBAAN,MAAoB;AAAA,EAChB,cAAc;AACV,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,eAAe;AAEpB,SAAK,aAAa,SAAS;AAC3B,SAAK,SAAS;AACd,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,MAAM,UAAU,OAAO,OAAO;AAC1B,SAAK,WAAW;AAChB,SAAK,eAAe,KAAK,SAAS,sBAAsB;AACxD,SAAK,cAAc,QAAQ,OAAO;AAClC,SAAK,cAAc,QAAQ,OAAO;AAClC,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,WAAW,OAAO,OAAO;AACrB,SAAK,SAAU,QAAQ,OAAO,UAAW,KAAK;AAC9C,SAAK,SAAU,QAAQ,OAAO,UAAW,KAAK;AAC9C,SAAK,iBAAiB;AAAA,EAC1B;AAAA;AAAA,EAEA,aAAa,MAAM;AACf,QAAI,MAAM;AACN,UAAI,CAAC,KAAK,WAAW;AACjB,YAAI,KAAK,UAAU;AACf,eAAK,SAAS,MAAM,UAAU;AAAA,QAClC;AACA,aAAK,YAAY;AACjB,aAAK,iBAAiB;AAAA,MAC1B;AAAA,IACJ,WACS,KAAK,WAAW;AACrB,UAAI,KAAK,UAAU;AACf,aAAK,SAAS,MAAM,UAAU;AAAA,MAClC;AACA,WAAK,YAAY;AAAA,IACrB;AAAA,EACJ;AAAA;AAAA,EAEA,KAAK,sBAAsB,UAAU;AACjC,QAAI,OAAO,MAAM;AACb,WAAK,QAAQ;AACb,eAAS;AAAA,IACb;AACA,QAAI,wBACA,KAAK,YACL,KAAK,aACL,KAAK;AAAA,KACJ,KAAK,UAAU,KAAK,SACvB;AACE,WAAK,kBAAkB,MAAM,KAAK,cAAc;AAAA,IACpD,OACK;AACD,iBAAW,MAAM,CAAC;AAAA,IACtB;AAAA,EACJ;AAAA,EACA,kBAAkB,UAAU,gBAAgB;AACxC,QAAI,WAAW,KAAK;AACpB,QAAI,oBAAoB,KAAK,SAAS,sBAAsB;AAC5D,aAAS,MAAM,aACX,SAAS,iBAAiB,aACZ,iBAAiB;AACnC,eAAW,UAAU;AAAA,MACjB,MAAM,kBAAkB;AAAA,MACxB,KAAK,kBAAkB;AAAA,IAC3B,CAAC;AACD,uBAAmB,UAAU,MAAM;AAC/B,eAAS,MAAM,aAAa;AAC5B,eAAS;AAAA,IACb,CAAC;AAAA,EACL;AAAA,EACA,UAAU;AACN,QAAI,KAAK,UAAU;AACf,oBAAc,KAAK,QAAQ;AAC3B,WAAK,WAAW;AAAA,IACpB;AACA,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,mBAAmB;AACf,QAAI,KAAK,YAAY,KAAK,WAAW;AACjC,iBAAW,KAAK,YAAY,GAAG;AAAA,QAC3B,MAAM,KAAK,aAAa,OAAO,KAAK;AAAA,QACpC,KAAK,KAAK,aAAa,MAAM,KAAK;AAAA,MACtC,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,cAAc;AACV,QAAI,eAAe,KAAK;AACxB,QAAI,WAAW,KAAK;AACpB,QAAI,CAAC,UAAU;AACX,iBAAW,KAAK,WAAW,KAAK,SAAS,UAAU,IAAI;AAGvD,eAAS,MAAM,aAAa;AAC5B,eAAS,MAAM,mBAAmB;AAClC,eAAS,MAAM,gBAAgB;AAC/B,eAAS,UAAU,IAAI,mBAAmB;AAC1C,iBAAW,UAAU;AAAA,QACjB,UAAU;AAAA,QACV,QAAQ,KAAK;AAAA,QACb,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,OAAO,aAAa,QAAQ,aAAa;AAAA,QACzC,QAAQ,aAAa,SAAS,aAAa;AAAA,QAC3C,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACZ,CAAC;AACD,WAAK,WAAW,YAAY,QAAQ;AAAA,IACxC;AACA,WAAO;AAAA,EACX;AACJ;AAUA,IAAM,kBAAN,cAA8B,iBAAiB;AAAA,EAC3C,YAAY,kBAAkB,eAAe;AACzC,UAAM;AACN,SAAK,eAAe,MAAM;AACtB,WAAK,YAAY,KAAK,iBAAiB,aAAa;AACpD,WAAK,aAAa,KAAK,iBAAiB,cAAc;AACtD,WAAK,mBAAmB;AAAA,IAC5B;AACA,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,YAAY,KAAK,gBAAgB,iBAAiB,aAAa;AACpE,SAAK,aAAa,KAAK,iBAAiB,iBAAiB,cAAc;AACvE,SAAK,cAAc,iBAAiB,eAAe;AACnD,SAAK,eAAe,iBAAiB,gBAAgB;AACrD,SAAK,cAAc,iBAAiB,eAAe;AACnD,SAAK,eAAe,iBAAiB,gBAAgB;AACrD,SAAK,aAAa,KAAK,kBAAkB;AACzC,QAAI,KAAK,eAAe;AACpB,WAAK,eAAe,EAAE,iBAAiB,UAAU,KAAK,YAAY;AAAA,IACtE;AAAA,EACJ;AAAA,EACA,UAAU;AACN,QAAI,KAAK,eAAe;AACpB,WAAK,eAAe,EAAE,oBAAoB,UAAU,KAAK,YAAY;AAAA,IACzE;AAAA,EACJ;AAAA,EACA,eAAe;AACX,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,gBAAgB;AACZ,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,aAAa,KAAK;AACd,SAAK,iBAAiB,aAAa,GAAG;AACtC,QAAI,CAAC,KAAK,eAAe;AAGrB,WAAK,YAAY,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,gBAAgB,CAAC,GAAG,CAAC;AAClE,WAAK,mBAAmB;AAAA,IAC5B;AAAA,EACJ;AAAA,EACA,cAAc,KAAK;AACf,SAAK,iBAAiB,cAAc,GAAG;AACvC,QAAI,CAAC,KAAK,eAAe;AAGrB,WAAK,aAAa,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,iBAAiB,CAAC,GAAG,CAAC;AACpE,WAAK,mBAAmB;AAAA,IAC5B;AAAA,EACJ;AAAA,EACA,iBAAiB;AACb,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,kBAAkB;AACd,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,iBAAiB;AACb,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,kBAAkB;AACd,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,qBAAqB;AAAA,EACrB;AACJ;AAEA,IAAM,yBAAN,cAAqC,gBAAgB;AAAA,EACjD,YAAY,IAAI,eAAe;AAC3B,UAAM,IAAI,wBAAwB,EAAE,GAAG,aAAa;AAAA,EACxD;AAAA,EACA,iBAAiB;AACb,WAAO,KAAK,iBAAiB;AAAA,EACjC;AAAA,EACA,oBAAoB;AAChB,WAAO,iBAAiB,KAAK,iBAAiB,EAAE;AAAA,EACpD;AACJ;AAEA,IAAM,wBAAN,cAAoC,gBAAgB;AAAA,EAChD,YAAY,eAAe;AACvB,UAAM,IAAI,uBAAuB,GAAG,aAAa;AAAA,EACrD;AAAA,EACA,iBAAiB;AACb,WAAO;AAAA,EACX;AAAA,EACA,oBAAoB;AAChB,WAAO;AAAA,MACH,MAAM,KAAK;AAAA,MACX,OAAO,KAAK,aAAa,KAAK;AAAA,MAC9B,KAAK,KAAK;AAAA,MACV,QAAQ,KAAK,YAAY,KAAK;AAAA,IAClC;AAAA,EACJ;AAAA;AAAA;AAAA,EAGA,qBAAqB;AACjB,SAAK,aAAa,KAAK,kBAAkB;AAAA,EAC7C;AACJ;AAKA,IAAM,UAAU,OAAO,gBAAgB,aAAa,YAAY,MAAM,KAAK;AAO3E,IAAM,eAAN,MAAmB;AAAA,EACf,cAAc;AAEV,SAAK,YAAY;AACjB,SAAK,cAAc,CAAC,QAAQ,cAAc;AAC1C,SAAK,gBAAgB;AACrB,SAAK,cAAc;AAEnB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,eAAe;AAEpB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AACtB,SAAK,UAAU,MAAM;AACjB,UAAI,KAAK,aAAa;AAClB,YAAI,OAAO,KAAK,gBAAgB,KAAK,iBAAiB,OAAO,SAAS,KAAK,iBAAiB,OAAO,OAAO;AAC1G,YAAI,MAAM;AACN,cAAI,MAAM,QAAQ;AAClB,eAAK,WAAW,OAAO,MAAM,KAAK,kBAAkB,GAAI;AACxD,eAAK,iBAAiB,GAAG;AAAA,QAC7B,OACK;AACD,eAAK,cAAc;AAAA,QACvB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,MAAM,OAAO,OAAO,eAAe;AAC/B,QAAI,KAAK,WAAW;AAChB,WAAK,eAAe,KAAK,YAAY,aAAa;AAClD,WAAK,iBAAiB;AACtB,WAAK,iBAAiB;AACtB,WAAK,cAAc;AACnB,WAAK,gBAAgB;AACrB,WAAK,gBAAgB;AACrB,WAAK,iBAAiB;AACtB,WAAK,WAAW,OAAO,KAAK;AAAA,IAChC;AAAA,EACJ;AAAA,EACA,WAAW,OAAO,OAAO;AACrB,QAAI,KAAK,WAAW;AAChB,UAAI,iBAAiB,QAAQ,OAAO;AACpC,UAAI,iBAAiB,QAAQ,OAAO;AACpC,UAAI,SAAS,KAAK,mBAAmB,OAAO,IAAI,iBAAiB,KAAK;AACtE,UAAI,SAAS,KAAK,mBAAmB,OAAO,IAAI,iBAAiB,KAAK;AACtE,UAAI,SAAS,GAAG;AACZ,aAAK,cAAc;AAAA,MACvB,WACS,SAAS,GAAG;AACjB,aAAK,gBAAgB;AAAA,MACzB;AACA,UAAI,SAAS,GAAG;AACZ,aAAK,gBAAgB;AAAA,MACzB,WACS,SAAS,GAAG;AACjB,aAAK,iBAAiB;AAAA,MAC1B;AACA,WAAK,iBAAiB;AACtB,WAAK,iBAAiB;AACtB,UAAI,CAAC,KAAK,aAAa;AACnB,aAAK,cAAc;AACnB,aAAK,iBAAiB,QAAQ,CAAC;AAAA,MACnC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,OAAO;AACH,QAAI,KAAK,WAAW;AAChB,WAAK,cAAc;AACnB,eAAS,eAAe,KAAK,cAAc;AACvC,oBAAY,QAAQ;AAAA,MACxB;AACA,WAAK,eAAe;AAAA,IACxB;AAAA,EACJ;AAAA,EACA,iBAAiB,KAAK;AAClB,SAAK,iBAAiB;AACtB,0BAAsB,KAAK,OAAO;AAAA,EACtC;AAAA,EACA,WAAW,MAAM,SAAS;AACtB,QAAI,EAAE,YAAY,IAAI;AACtB,QAAI,EAAE,cAAc,IAAI;AACxB,QAAI,cAAc,gBAAgB,KAAK;AACvC,QAAI;AAAA;AAAA,MACD,cAAc,eAAgB,gBAAgB;AAAA,MAC7C,KAAK,cAAc;AAAA;AACvB,QAAI,OAAO;AACX,YAAQ,KAAK,MAAM;AAAA,MACf,KAAK;AACD,eAAO;AAAA;AAAA,MAEX,KAAK;AACD,oBAAY,cAAc,YAAY,cAAc,IAAI,WAAW,IAAI;AACvE;AAAA,MACJ,KAAK;AACD,eAAO;AAAA;AAAA,MAEX,KAAK;AACD,oBAAY,aAAa,YAAY,aAAa,IAAI,WAAW,IAAI;AACrE;AAAA,IACR;AAAA,EACJ;AAAA;AAAA,EAEA,gBAAgB,MAAM,KAAK;AACvB,QAAI,EAAE,cAAc,IAAI;AACxB,QAAI,WAAW;AACf,QAAI,eAAe,KAAK,gBAAgB,CAAC;AACzC,aAAS,eAAe,cAAc;AAClC,UAAI,OAAO,YAAY;AACvB,UAAI,WAAW,OAAO,KAAK;AAC3B,UAAI,YAAY,KAAK,QAAQ;AAC7B,UAAI,UAAU,MAAM,KAAK;AACzB,UAAI,aAAa,KAAK,SAAS;AAE/B,UAAI,YAAY,KAAK,aAAa,KAAK,WAAW,KAAK,cAAc,GAAG;AACpE,YAAI,WAAW,iBAAiB,KAAK,eAAe,YAAY,YAAY,MACvE,CAAC,YAAY,SAAS,WAAW,UAAU;AAC5C,qBAAW,EAAE,aAAa,MAAM,OAAO,UAAU,QAAQ;AAAA,QAC7D;AACA,YAAI,cAAc,iBAAiB,KAAK,iBAAiB,YAAY,cAAc,MAC9E,CAAC,YAAY,SAAS,WAAW,aAAa;AAC/C,qBAAW,EAAE,aAAa,MAAM,UAAU,UAAU,WAAW;AAAA,QACnE;AAKA,YAAI,YAAY,iBAAiB,KAAK,iBAAiB,YAAY,cAAc,MAC5E,CAAC,YAAY,SAAS,WAAW,WAAW;AAC7C,qBAAW,EAAE,aAAa,MAAM,QAAQ,UAAU,SAAS;AAAA,QAC/D;AACA,YAAI,aAAa,iBAAiB,KAAK,kBAAkB,YAAY,eAAe,MAC/E,CAAC,YAAY,SAAS,WAAW,YAAY;AAC9C,qBAAW,EAAE,aAAa,MAAM,SAAS,UAAU,UAAU;AAAA,QACjE;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,YAAY,eAAe;AACvB,WAAO,KAAK,eAAe,aAAa,EAAE,IAAI,CAAC,OAAO;AAClD,UAAI,OAAO,QAAQ;AACf,eAAO,IAAI,sBAAsB,KAAK;AAAA,MAC1C;AACA,aAAO,IAAI,uBAAuB,IAAI,KAAK;AAAA,IAC/C,CAAC;AAAA,EACL;AAAA,EACA,eAAe,eAAe;AAC1B,QAAI,MAAM,CAAC;AACX,aAAS,SAAS,KAAK,aAAa;AAChC,UAAI,OAAO,UAAU,UAAU;AAC3B,YAAI,KAAK,KAAK;AAAA,MAClB,OACK;AAKD,YAAI,KAAK,GAAG,MAAM,UAAU,MAAM,KAAK,cAAc,YAAY,EAAE,iBAAiB,KAAK,CAAC,CAAC;AAAA,MAC/F;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AAQA,IAAM,4BAAN,cAAwC,gBAAgB;AAAA,EACpD,YAAY,aAAa,UAAU;AAC/B,UAAM,WAAW;AACjB,SAAK,cAAc;AAGnB,SAAK,QAAQ;AACb,SAAK,cAAc;AACnB,SAAK,qBAAqB;AAC1B,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,sBAAsB;AAC3B,SAAK,iBAAiB;AACtB,SAAK,gBAAgB,CAAC,OAAO;AACzB,UAAI,CAAC,KAAK,YAAY;AAClB,aAAK,gBAAgB;AACrB,aAAK,eAAe;AACpB,aAAK,sBAAsB;AAC3B,yBAAiB,SAAS,IAAI;AAC9B,2BAAmB,SAAS,IAAI;AAIhC,YAAI,CAAC,GAAG,SAAS;AACb,aAAG,UAAU,eAAe;AAAA,QAChC;AACA,aAAK,QAAQ,QAAQ,eAAe,EAAE;AACtC,YAAI,KAAK;AAAA,QACL,CAAC,KAAK,QAAQ,kBAAkB;AAEhC,eAAK,OAAO,aAAa,KAAK;AAC9B,eAAK,OAAO,MAAM,GAAG,WAAW,GAAG,OAAO,GAAG,KAAK;AAClD,eAAK,WAAW,EAAE;AAClB,cAAI,CAAC,KAAK,aAAa;AACnB,iBAAK,wBAAwB,EAAE;AAAA,UACnC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,gBAAgB,CAAC,OAAO;AACzB,UAAI,KAAK,eAAe;AACpB,aAAK,QAAQ,QAAQ,eAAe,EAAE;AACtC,YAAI,CAAC,KAAK,qBAAqB;AAC3B,cAAI,cAAc,KAAK;AACvB,cAAI;AACJ,cAAI,EAAE,QAAQ,OAAO,IAAI;AACzB,uBAAa,SAAS,SAAS,SAAS;AACxC,cAAI,cAAc,cAAc,aAAa;AACzC,iBAAK,wBAAwB,EAAE;AAAA,UACnC;AAAA,QACJ;AACA,YAAI,KAAK,YAAY;AAEjB,cAAI,GAAG,UAAU,SAAS,UAAU;AAChC,iBAAK,OAAO,WAAW,GAAG,OAAO,GAAG,KAAK;AACzC,iBAAK,aAAa,WAAW,GAAG,OAAO,GAAG,KAAK;AAAA,UACnD;AACA,eAAK,QAAQ,QAAQ,YAAY,EAAE;AAAA,QACvC;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,cAAc,CAAC,OAAO;AACvB,UAAI,KAAK,eAAe;AACpB,aAAK,gBAAgB;AACrB,uBAAe,SAAS,IAAI;AAC5B,yBAAiB,SAAS,IAAI;AAC9B,aAAK,QAAQ,QAAQ,aAAa,EAAE;AACpC,YAAI,KAAK,YAAY;AACjB,eAAK,aAAa,KAAK;AACvB,eAAK,YAAY,EAAE;AAAA,QACvB;AACA,YAAI,KAAK,gBAAgB;AACrB,uBAAa,KAAK,cAAc;AAChC,eAAK,iBAAiB;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,UAAU,KAAK,UAAU,IAAI,gBAAgB,WAAW;AAC5D,YAAQ,QAAQ,GAAG,eAAe,KAAK,aAAa;AACpD,YAAQ,QAAQ,GAAG,eAAe,KAAK,aAAa;AACpD,YAAQ,QAAQ,GAAG,aAAa,KAAK,WAAW;AAChD,QAAI,UAAU;AACV,cAAQ,WAAW;AAAA,IACvB;AACA,SAAK,SAAS,IAAI,cAAc;AAChC,SAAK,eAAe,IAAI,aAAa;AAAA,EACzC;AAAA,EACA,UAAU;AACN,SAAK,QAAQ,QAAQ;AAGrB,SAAK,YAAY,CAAC,CAAC;AAAA,EACvB;AAAA,EACA,WAAW,IAAI;AACX,QAAI,OAAO,KAAK,UAAU,UAAU;AAChC,WAAK,iBAAiB,WAAW,MAAM;AACnC,aAAK,iBAAiB;AACtB,aAAK,eAAe,EAAE;AAAA,MAC1B,GAAG,KAAK,KAAK;AAAA,IACjB,OACK;AACD,WAAK,eAAe,EAAE;AAAA,IAC1B;AAAA,EACJ;AAAA,EACA,eAAe,IAAI;AACf,SAAK,eAAe;AACpB,SAAK,aAAa,EAAE;AAAA,EACxB;AAAA,EACA,wBAAwB,IAAI;AACxB,SAAK,sBAAsB;AAC3B,SAAK,aAAa,EAAE;AAAA,EACxB;AAAA,EACA,aAAa,IAAI;AACb,QAAI,KAAK,gBAAgB,KAAK,qBAAqB;AAC/C,UAAI,CAAC,KAAK,QAAQ,kBAAkB,KAAK,oBAAoB;AACzD,aAAK,aAAa;AAClB,aAAK,oBAAoB;AACzB,aAAK,aAAa,MAAM,GAAG,OAAO,GAAG,OAAO,KAAK,WAAW;AAC5D,aAAK,QAAQ,QAAQ,aAAa,EAAE;AACpC,YAAI,KAAK,uBAAuB,OAAO;AACnC,eAAK,QAAQ,kBAAkB;AAAA,QACnC;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,YAAY,IAAI;AAGZ,SAAK,OAAO,KAAK,KAAK,mBAAmB,KAAK,SAAS,KAAK,MAAM,EAAE,CAAC;AAAA,EACzE;AAAA,EACA,SAAS,IAAI;AACT,SAAK,aAAa;AAClB,SAAK,QAAQ,QAAQ,WAAW,EAAE;AAAA,EACtC;AAAA;AAAA,EAEA,cAAc,MAAM;AAChB,SAAK,QAAQ,mBAAmB;AAAA,EACpC;AAAA,EACA,mBAAmB,MAAM;AACrB,SAAK,OAAO,aAAa,IAAI;AAAA,EACjC;AAAA,EACA,qBAAqB,MAAM;AACvB,SAAK,oBAAoB;AAAA,EAC7B;AAAA,EACA,qBAAqB,MAAM;AACvB,SAAK,aAAa,YAAY;AAAA,EAClC;AACJ;AAUA,IAAM,gBAAN,MAAoB;AAAA,EAChB,YAAY,IAAI;AACZ,SAAK,KAAK;AACV,SAAK,WAAW,YAAY,EAAE;AAE9B,SAAK,eAAe,mBAAmB,EAAE,EAAE,IAAI,CAAC,aAAa,IAAI,uBAAuB,UAAU,IAAI,CAAC;AAAA,EAC3G;AAAA,EACA,UAAU;AACN,aAAS,eAAe,KAAK,cAAc;AACvC,kBAAY,QAAQ;AAAA,IACxB;AAAA,EACJ;AAAA,EACA,cAAc;AACV,QAAI,OAAO,KAAK,SAAS;AACzB,aAAS,eAAe,KAAK,cAAc;AACvC,cAAQ,YAAY,iBAAiB,YAAY,cAAc;AAAA,IACnE;AACA,WAAO;AAAA,EACX;AAAA,EACA,aAAa;AACT,QAAI,MAAM,KAAK,SAAS;AACxB,aAAS,eAAe,KAAK,cAAc;AACvC,aAAO,YAAY,gBAAgB,YAAY,aAAa;AAAA,IAChE;AACA,WAAO;AAAA,EACX;AAAA,EACA,iBAAiB,OAAO,OAAO;AAC3B,QAAI,QAAQ,EAAE,MAAM,OAAO,KAAK,MAAM;AACtC,aAAS,eAAe,KAAK,cAAc;AACvC,UAAI,CAAC,kBAAkB,YAAY,eAAe,CAAC,KAC/C,CAAC,gBAAgB,OAAO,YAAY,UAAU,GAAG;AACjD,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AAGA,SAAS,kBAAkB,MAAM;AAC7B,MAAI,UAAU,KAAK;AACnB,SAAO,YAAY,UAAU,YAAY;AAC7C;AAeA,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,UAAU,gBAAgB;AAElC,SAAK,mBAAmB;AACxB,SAAK,iBAAiB;AACtB,SAAK,oBAAoB;AACzB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,oBAAoB,CAAC,OAAO;AAC7B,UAAI,EAAE,UAAAA,UAAS,IAAI;AACnB,WAAK,aAAa;AAClB,WAAK,YAAY;AACjB,WAAK,WAAW;AAChB,WAAK,YAAY;AACjB,WAAK,kBAAkB,EAAE;AACzB,UAAI,KAAK,cAAc,CAAC,KAAK,gBAAgB;AACzC,QAAAA,UAAS,cAAc,KAAK;AAE5B,aAAK,QAAQ,QAAQ,eAAe,EAAE;AAAA,MAC1C,OACK;AACD,QAAAA,UAAS,cAAc,IAAI;AAAA,MAC/B;AAAA,IACJ;AACA,SAAK,kBAAkB,CAAC,OAAO;AAC3B,WAAK,QAAQ,QAAQ,aAAa,EAAE;AACpC,WAAK,WAAW,IAAI,IAAI;AAAA,IAC5B;AACA,SAAK,iBAAiB,CAAC,OAAO;AAC1B,WAAK,QAAQ,QAAQ,YAAY,EAAE;AACnC,WAAK,WAAW,EAAE;AAAA,IACtB;AACA,SAAK,kBAAkB,CAAC,OAAO;AAC3B,WAAK,YAAY;AACjB,WAAK,QAAQ,QAAQ,aAAa,EAAE;AAAA,IACxC;AACA,SAAK,gBAAgB,CAAC,OAAO;AACzB,UAAI,KAAK,WAAW;AAChB,aAAK,QAAQ,QAAQ,aAAa,MAAM,MAAM,EAAE;AAAA,MACpD;AACA,WAAK,WAAW,KAAK;AACrB,WAAK,YAAY;AACjB,WAAK,QAAQ,QAAQ,WAAW,EAAE;AAAA,IACtC;AACA,SAAK,iBAAiB;AACtB,aAAS,QAAQ,GAAG,eAAe,KAAK,iBAAiB;AACzD,aAAS,QAAQ,GAAG,aAAa,KAAK,eAAe;AACrD,aAAS,QAAQ,GAAG,YAAY,KAAK,cAAc;AACnD,aAAS,QAAQ,GAAG,aAAa,KAAK,eAAe;AACrD,aAAS,QAAQ,GAAG,WAAW,KAAK,aAAa;AACjD,SAAK,WAAW;AAChB,SAAK,UAAU,IAAI,QAAQ;AAAA,EAC/B;AAAA;AAAA;AAAA,EAGA,kBAAkB,IAAI;AAClB,QAAI,YAAY,EAAE,MAAM,GAAG,OAAO,KAAK,GAAG,MAAM;AAChD,QAAI,gBAAgB;AACpB,QAAI,YAAY,GAAG;AACnB,QAAI;AACJ,QAAI,qBAAqB,aAAa;AAClC,oBAAc,YAAY,SAAS;AACnC,sBAAgB,eAAe,eAAe,WAAW;AAAA,IAC7D;AACA,QAAI,aAAa,KAAK,aAAa,KAAK,kBAAkB,cAAc,MAAM,cAAc,GAAG;AAC/F,QAAI,YAAY;AACZ,UAAI,KAAK,oBAAoB,aAAa;AACtC,YAAI,oBAAoB,eAAe,aAAa,WAAW,IAAI;AACnE,YAAI,mBAAmB;AACnB,0BAAgB,cAAc,iBAAiB;AAAA,QACnD;AAAA,MACJ;AACA,WAAK,cAAc,WAAW,eAAe,SAAS;AAAA,IAC1D,OACK;AACD,WAAK,cAAc,EAAE,MAAM,GAAG,KAAK,EAAE;AAAA,IACzC;AAAA,EACJ;AAAA,EACA,WAAW,IAAI,aAAa;AACxB,QAAI,MAAM,KAAK,kBAAkB,GAAG,QAAQ,KAAK,YAAY,MAAM,GAAG,QAAQ,KAAK,YAAY,GAAG;AAClG,QAAI,eAAe,CAAC,YAAY,KAAK,WAAW,GAAG,GAAG;AAClD,WAAK,YAAY;AACjB,WAAK,QAAQ,QAAQ,aAAa,KAAK,OAAO,EAAE;AAAA,IACpD;AAAA,EACJ;AAAA,EACA,cAAc;AACV,SAAK,iBAAiB,QAAQ,KAAK,gBAAgB,CAAC,wBAAwB;AACxE,0BAAoB,UAAU,YAAY;AAC1C,aAAO,IAAI,cAAc,oBAAoB,EAAE;AAAA,IACnD,CAAC;AAAA,EACL;AAAA,EACA,cAAc;AACV,QAAI,EAAE,eAAe,IAAI;AACzB,aAAS,MAAM,gBAAgB;AAC3B,qBAAe,EAAE,EAAE,QAAQ;AAAA,IAC/B;AACA,SAAK,iBAAiB,CAAC;AAAA,EAC3B;AAAA,EACA,kBAAkB,YAAY,WAAW;AACrC,QAAI,EAAE,gBAAgB,eAAe,IAAI;AACzC,QAAI,UAAU;AACd,aAAS,MAAM,gBAAgB;AAC3B,UAAI,YAAY,eAAe,EAAE,EAAE;AACnC,UAAI,gBAAgB,eAAe,EAAE;AACrC,UAAI;AAAA,MACA,cAAc,iBAAiB,YAAY,SAAS,GAAG;AACvD,YAAI,aAAa,cAAc,YAAY;AAC3C,YAAI,YAAY,cAAc,WAAW;AACzC,YAAI,eAAe,aAAa;AAChC,YAAI,cAAc,YAAY;AAC9B,YAAI,EAAE,SAAS,IAAI;AACnB,YAAI,QAAQ,SAAS,QAAQ,SAAS;AACtC,YAAI,SAAS,SAAS,SAAS,SAAS;AACxC;AAAA;AAAA,UAEA,gBAAgB,KAAK,eAAe,SAChC,eAAe,KAAK,cAAc;AAAA,UAAQ;AAC1C,cAAI,MAAM,UAAU,SAAS,cAAc,aAAa,OAAO,MAAM;AACrE,cAAI;AAAA,UAEJ,mBAAmB,IAAI,YAAY,aAAa,IAAI,SAAS,KAAK;AAAA;AAAA;AAAA,WAI7D,KAAK,qBACF,cAAc,GAAG,SAAS,cAAc,GAAG,YAAY,EAAE;AAAA;AAAA,YAEzD,eAAe,aAAa,OAAO;AAAA,YAAS,cAAc,YAAY,OAAO;AAAA,UAAO,CAAC,OACxF,CAAC,WAAW,IAAI,QAAQ,QAAQ,QAAQ;AACzC,gBAAI,cAAc;AAClB,gBAAI,UAAU,UAAU;AAExB,gBAAI,KAAK,QAAQ;AACjB,gBAAI,KAAK,SAAS;AAClB,gBAAI,KAAK,OAAO;AAChB,gBAAI,KAAK,UAAU;AACnB,sBAAU;AAAA,UACd;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAAS,YAAY,MAAM,MAAM;AAC7B,MAAI,CAAC,QAAQ,CAAC,MAAM;AAChB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,IAAI,MAAM,QAAQ,IAAI,GAAG;AACjC,WAAO;AAAA,EACX;AACA,SAAO,iBAAiB,KAAK,UAAU,KAAK,QAAQ;AACxD;AAEA,SAAS,6BAA6B,UAAU,SAAS;AACrD,MAAI,QAAQ,CAAC;AACb,WAAS,aAAa,QAAQ,YAAY,qBAAqB;AAC3D,WAAO,OAAO,OAAO,UAAU,UAAU,OAAO,CAAC;AAAA,EACrD;AACA,SAAO,OAAO,OAAO,kBAAkB,UAAU,QAAQ,OAAO,CAAC;AACjE,SAAO;AACX;AACA,SAAS,kBAAkB,MAAM,SAAS;AACtC,SAAO;AAAA,IACH,MAAM,QAAQ,OAAO,KAAK,MAAM,KAAK;AAAA,IACrC,SAAS,QAAQ,UAAU,KAAK,MAAM,OAAO,EAAE,UAAU,KAAK,OAAO,CAAC;AAAA,IACtE,QAAQ,KAAK;AAAA,EACjB;AACJ;AAMA,IAAM,eAAN,cAA2B,YAAY;AAAA,EACnC,YAAY,UAAU;AAClB,UAAM,QAAQ;AACd,SAAK,oBAAoB,CAAC,QAAQ;AAC9B,UAAI,EAAE,SAAS,IAAI;AACnB,UAAI,SAAS,IAAI,UAAU;AAE3B,eAAS,cAAc,CAAC,KAAK,UAAU,kBAAkB,MAAM,CAAC;AAAA,IACpE;AAEA,SAAK,gBAAgB,CAAC,OAAO;AACzB,UAAI,EAAE,UAAU,IAAI;AACpB,UAAI,EAAE,QAAQ,IAAI,KAAK;AACvB,UAAI,CAAC,QAAQ,gBAAgB;AACzB,YAAI,EAAE,YAAY,SAAS,IAAI,KAAK;AACpC,YAAI,cAAc,YAAY,YAAY,YAAY,QAAQ,GAAG;AAC7D,cAAI,EAAE,QAAQ,IAAI;AAClB,cAAI,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,6BAA6B,WAAW,UAAU,OAAO,CAAC,GAAG,EAAE,OAAO,WAAW,OAAO,SAAS,GAAG,WAAW,MAAM,QAAQ,WAAW,QAAQ,YAAY,KAAK,CAAC;AAC5M,kBAAQ,QAAQ,QAAQ,aAAa,GAAG;AAAA,QAC5C;AAAA,MACJ;AAAA,IACJ;AAEA,SAAK,WAAW,IAAI,0BAA0B,SAAS,EAAE;AACzD,SAAK,SAAS,aAAa,YAAY;AACvC,QAAI,cAAc,KAAK,cAAc,IAAI,YAAY,KAAK,UAAU,2BAA2B,QAAQ,CAAC;AACxG,gBAAY,QAAQ,GAAG,eAAe,KAAK,iBAAiB;AAC5D,gBAAY,QAAQ,GAAG,WAAW,KAAK,aAAa;AAAA,EACxD;AAAA,EACA,UAAU;AACN,SAAK,SAAS,QAAQ;AAAA,EAC1B;AACJ;AAMA,IAAM,gBAAN,cAA4B,YAAY;AAAA,EACpC,YAAY,UAAU;AAClB,UAAM,QAAQ;AACd,SAAK,gBAAgB;AACrB,SAAK,oBAAoB,CAAC,OAAO;AAC7B,UAAI,EAAE,WAAAC,YAAW,UAAAD,UAAS,IAAI;AAC9B,UAAI,EAAE,SAAAE,SAAQ,IAAID,WAAU;AAC5B,UAAI,YAAYC,SAAQ,cACpBD,WAAU,kBAAkB,GAAG,UAAU,MAAM;AAEnD,MAAAD,UAAS,cAAc,CAAC,SAAS;AAEjC,MAAAA,UAAS,QAAQ,GAAG,UAAU,yBAAyBC,UAAS,IAAI;AAAA,IACxE;AACA,SAAK,kBAAkB,CAAC,OAAO;AAC3B,WAAK,UAAU,QAAQ,YAAY,SAAS,EAAE;AAAA,IAClD;AACA,SAAK,kBAAkB,CAAC,KAAK,YAAY;AACrC,UAAI,EAAE,QAAQ,IAAI,KAAK;AACvB,UAAI,gBAAgB;AACpB,UAAI,YAAY;AAChB,UAAI,KAAK;AACL,YAAI,aAAa,KAAK,YAAY;AAClC,YAAI,aAAa,IAAI,gBAAgB,WAAW,eACzC,KAAK,qBACL,CAAC,KAAK,kBAAkB,YAAY,GAAG;AAC9C,YAAI,CAAC,YAAY;AACb,0BAAgB,sBAAsB,YAAY,KAAK,QAAQ,YAAY,yBAAyB;AAAA,QACxG;AACA,YAAI,CAAC,iBAAiB,CAAC,qBAAqB,eAAe,IAAI,aAAa,OAAO,GAAG;AAClF,sBAAY;AACZ,0BAAgB;AAAA,QACpB;AAAA,MACJ;AACA,UAAI,eAAe;AACf,gBAAQ,SAAS,EAAE,MAAM,gBAAgB,WAAW,cAAc,CAAC;AAAA,MACvE,WACS,CAAC,SAAS;AACf,gBAAQ,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAAA,MAC/C;AACA,UAAI,CAAC,WAAW;AACZ,qBAAa;AAAA,MACjB,OACK;AACD,sBAAc;AAAA,MAClB;AACA,UAAI,CAAC,SAAS;AACV,aAAK,gBAAgB;AAAA,MACzB;AAAA,IACJ;AACA,SAAK,kBAAkB,CAAC,QAAQ;AAC5B,UAAI,KAAK,eAAe;AAEpB,0BAAkB,KAAK,eAAe,KAAK,KAAK,UAAU,OAAO;AACjE,aAAK,gBAAgB;AAAA,MACzB;AAAA,IACJ;AACA,QAAI,EAAE,UAAU,IAAI;AACpB,QAAI,EAAE,QAAQ,IAAI,UAAU;AAC5B,QAAI,WAAW,KAAK,WAAW,IAAI,0BAA0B,SAAS,EAAE;AACxE,aAAS,qBAAqB;AAC9B,aAAS,cAAc,QAAQ,qBAAqB;AACpD,aAAS,aAAa,YAAY,QAAQ;AAC1C,QAAI,cAAc,KAAK,cAAc,IAAI,YAAY,KAAK,UAAU,2BAA2B,QAAQ,CAAC;AACxG,gBAAY,QAAQ,GAAG,eAAe,KAAK,iBAAiB;AAC5D,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AAAA,EAC5D;AAAA,EACA,UAAU;AACN,SAAK,SAAS,QAAQ;AAAA,EAC1B;AACJ;AACA,SAAS,yBAAyB,WAAW;AACzC,MAAI,EAAE,QAAQ,IAAI,UAAU;AAC5B,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,MAAM;AACf,YAAQ,QAAQ;AAAA,EACpB;AACA,SAAO;AACX;AACA,SAAS,sBAAsB,MAAM,MAAM,2BAA2B;AAClE,MAAI,YAAY,KAAK;AACrB,MAAI,YAAY,KAAK;AACrB,MAAI,KAAK;AAAA,IACL,UAAU,MAAM;AAAA,IAChB,UAAU,MAAM;AAAA,IAChB,UAAU,MAAM;AAAA,IAChB,UAAU,MAAM;AAAA,EACpB;AACA,KAAG,KAAK,cAAc;AACtB,MAAI,QAAQ,CAAC;AACb,WAAS,eAAe,2BAA2B;AAC/C,QAAI,MAAM,YAAY,MAAM,IAAI;AAChC,QAAI,QAAQ,OAAO;AACf,aAAO;AAAA,IACX;AACA,QAAI,KAAK;AACL,aAAO,OAAO,OAAO,GAAG;AAAA,IAC5B;AAAA,EACJ;AACA,QAAM,QAAQ,EAAE,OAAO,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE;AACzC,QAAM,SAAS,UAAU;AACzB,SAAO;AACX;AAEA,IAAM,gBAAN,MAAM,uBAAsB,YAAY;AAAA,EACpC,YAAY,UAAU;AAClB,UAAM,QAAQ;AAEd,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAC7B,SAAK,oBAAoB,CAAC,OAAO;AAC7B,UAAI,aAAa,GAAG,UAAU;AAC9B,UAAI,EAAE,WAAAA,YAAW,UAAAD,UAAS,IAAI;AAC9B,UAAI,EAAE,OAAO,IAAIA;AACjB,UAAI,EAAE,SAAAE,SAAQ,IAAID,WAAU;AAC5B,UAAI,iBAAiBA,WAAU;AAC/B,WAAK,YAAY,GAAG;AACpB,UAAI,aAAa,KAAK,aAAa,SAAS,GAAG,SAAS;AACxD,UAAI,aAAa,KAAK,aAAa,WAAW;AAC9C,UAAI,kBAAkB,WAAW,SAAS;AAC1C,WAAK,iBAAiB,kBAAkB,eAAe,eAAe,EAAE,YAAY,eAAe;AACnG,MAAAD,UAAS,cAAc,GAAG,UAAU,IAAIE,SAAQ;AAChD,MAAAF,UAAS;AAAA,MAEJ,GAAG,WAAW,oBAAoBC,WAAU,MAAM,iBAC/C,uBAAuBA,UAAS,IAChC;AACR,UAAIC,SAAQ,mBAAmB;AAC3B,eAAO,aAAaA,SAAQ;AAAA,MAChC,OACK;AACD,eAAO,aAAa,eAAe,YAAY,KAAK;AAAA,MACxD;AACA,aAAO,iBAAiBA,SAAQ;AAChC,UAAI,UAAUD,WAAU,iBAAiB,UAAU,KAC/C,CAAC,eAAe,YAAY,mBAAmB;AACnD,MAAAD,UAAS,cAAc,CAAC,OAAO;AAG/B,WAAK,aAAa,WACd,GAAG,UAAU,UAAU,SAAS,oBAAoB;AAAA,IAC5D;AACA,SAAK,kBAAkB,CAAC,OAAO;AAC3B,UAAI,iBAAiB,KAAK,UAAU;AACpC,UAAI,aAAa,KAAK;AACtB,UAAI,kBAAkB,WAAW,SAAS;AAC1C,UAAI,GAAG,SAAS;AAEZ,YAAI,oBAAoB,KAAK,UAAU,MAAM,gBAAgB;AACzD,yBAAe,SAAS,EAAE,MAAM,gBAAgB,gBAAgB,CAAC;AAAA,QACrE;AAAA,MACJ,OACK;AAED,uBAAe,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAAA,MACtD;AACA,UAAI,KAAK,YAAY;AACjB,uBAAe,YAAY,SAAS,EAAE;AACtC,uBAAe,QAAQ,QAAQ,kBAAkB;AAAA,UAC7C,IAAI,KAAK;AAAA,UACT,OAAO,IAAI,UAAU,gBAAgB,WAAW,KAAK,WAAW,QAAQ;AAAA,UACxE,SAAS,GAAG;AAAA,UACZ,MAAM,eAAe;AAAA,QACzB,CAAC;AAAA,MACL;AAAA,IACJ;AACA,SAAK,kBAAkB,CAAC,KAAK,YAAY;AACrC,UAAI,CAAC,KAAK,YAAY;AAClB;AAAA,MACJ;AACA,UAAI,iBAAiB,KAAK;AAC1B,UAAI,aAAa,KAAK,YAAY;AAClC,UAAI,iBAAiB,KAAK,UAAU;AAEpC,UAAI,mBAAmB;AACvB,UAAI,WAAW;AACf,UAAI,wBAAwB;AAC5B,UAAI,YAAY;AAChB,UAAI,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,eAAe,sBAAsB;AAAA,QACrC,SAAS;AAAA,MACb;AACA,UAAI,KAAK;AACL,2BAAmB,IAAI;AACvB,YAAI,mBAAmB,iBAAiB;AACxC,YAAI,mBAAmB,oBAClB,iBAAiB,YAAY,iBAAiB,WAAY;AAC3D,qBAAW,qBAAqB,YAAY,KAAK,KAAK,WAAW,SAAS,MAAM,OAAO,iBAAiB,eAAe,EAAE,YAAY,0BAA0B;AAC/J,cAAI,UAAU;AACV,oCAAwB,0BAA0B,gBAAgB,iBAAiB,eAAe,EAAE,cAAc,UAAU,gBAAgB;AAC5I,wBAAY,gBAAgB;AAC5B,gBAAI,CAAC,mBAAmB,aAAa,IAAI,aAAa,gBAAgB,GAAG;AACrE,0BAAY;AACZ,yBAAW;AACX,sCAAwB;AACxB,0BAAY,gBAAgB,sBAAsB;AAAA,YACtD;AAAA,UACJ;AAAA,QACJ,OACK;AACD,6BAAmB;AAAA,QACvB;AAAA,MACJ;AACA,WAAK,YAAY,kBAAkB,WAAW;AAC9C,UAAI,CAAC,WAAW;AACZ,qBAAa;AAAA,MACjB,OACK;AACD,sBAAc;AAAA,MAClB;AACA,UAAI,CAAC,SAAS;AACV,YAAI,mBAAmB;AAAA,QACnB,YAAY,YAAY,GAAG,GAAG;AAC9B,qBAAW;AAAA,QACf;AACA,aAAK,SAAS,qBAAqB,CAAC,QAAQ;AAG5C,aAAK,SAAS,mBAAmB,CAAC,OAAO,CAAC,KAAK,UAAU,YAAY,EAAE,cAAc,kBAAkB,CAAC;AAExG,aAAK,mBAAmB;AACxB,aAAK,gBAAgB;AACrB,aAAK,wBAAwB;AAAA,MACjC;AAAA,IACJ;AACA,SAAK,kBAAkB,MAAM;AACzB,UAAI,CAAC,KAAK,YAAY;AAClB,aAAK,QAAQ;AAAA,MACjB;AAAA,IACJ;AACA,SAAK,gBAAgB,CAAC,OAAO;AACzB,UAAI,KAAK,YAAY;AACjB,YAAI,iBAAiB,KAAK,UAAU;AACpC,YAAI,cAAc,eAAe;AACjC,YAAI,EAAE,kBAAkB,cAAc,IAAI;AAC1C,YAAI,WAAW,KAAK,WAAW;AAC/B,YAAI,gBAAgB,KAAK,WAAW;AACpC,YAAI,WAAW,IAAI,UAAU,gBAAgB,UAAU,aAAa;AACpE,YAAI,iBAAiB,KAAK;AAC1B,YAAI,wBAAwB,KAAK;AACjC,YAAI,EAAE,SAAS,IAAI,KAAK;AACxB,aAAK,UAAU;AACf,uBAAe,QAAQ,QAAQ,iBAAiB;AAAA,UAC5C,IAAI,KAAK;AAAA,UACT,OAAO;AAAA,UACP,SAAS,GAAG;AAAA,UACZ,MAAM;AAAA,QACV,CAAC;AACD,YAAI,eAAe;AAEf,cAAI,qBAAqB,gBAAgB;AACrC,gBAAI,kBAAkB,IAAI,UAAU,gBAAgB,sBAAsB,KAAK,SAAS,KAAK,GAAG,gBAAgB,sBAAsB,UAAU,cAAc,UAAU,IAAI,IAAI;AAChL,2BAAe,SAAS;AAAA,cACpB,MAAM;AAAA,cACN,YAAY;AAAA,YAChB,CAAC;AACD,gBAAI,iBAAiB;AAAA,cACjB,UAAU;AAAA,cACV,OAAO;AAAA,cACP,eAAe,eAAe,uBAAuB,gBAAgB,aAAa;AAAA,cAClF,SAAS;AACL,+BAAe,SAAS;AAAA,kBACpB,MAAM;AAAA,kBACN,YAAY;AAAA;AAAA,gBAChB,CAAC;AAAA,cACL;AAAA,YACJ;AACA,gBAAI,cAAc,CAAC;AACnB,qBAAS,eAAe,eAAe,eAAe,EAAE,YAAY,uBAAuB;AACvF,qBAAO,OAAO,aAAa,YAAY,eAAe,cAAc,CAAC;AAAA,YACzE;AACA,2BAAe,QAAQ,QAAQ,aAAa,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,cAAc,GAAG,WAAW,GAAG,EAAE,IAAI,GAAG,WAAW,OAAO,cAAc,YAAY,SAAS,GAAG,WAAW,MAAM,YAAY,CAAC,CAAC;AACzN,2BAAe,QAAQ,QAAQ,eAAe,cAAc;AAAA,UAEhE,WACS,kBAAkB;AACvB,gBAAI,iBAAiB;AAAA,cACjB,OAAO;AAAA,cACP,eAAe,eAAe,gBAAgB,gBAAgB,aAAa;AAAA,cAC3E,SAAS;AACL,+BAAe,SAAS;AAAA,kBACpB,MAAM;AAAA,kBACN,YAAY;AAAA,gBAChB,CAAC;AAAA,cACL;AAAA,YACJ;AACA,2BAAe,QAAQ,QAAQ,cAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,cAAc,GAAG,EAAE,WAAW,GAAG,WAAW,MAAM,YAAY,CAAC,CAAC;AAC7I,2BAAe,SAAS;AAAA,cACpB,MAAM;AAAA,cACN,YAAY;AAAA,YAChB,CAAC;AACD,2BAAe,QAAQ,QAAQ,eAAe,cAAc;AAC5D,gBAAI,gBAAgB,sBAAsB,KAAK,SAAS,KAAK;AAC7D,gBAAI,qBAAqB,sBAAsB,UAAU,cAAc,UAAU;AACjF,gBAAI,gBAAgB,IAAI,UAAU,kBAAkB,eAAe,kBAAkB;AACrF,6BAAiB,SAAS;AAAA,cACtB,MAAM;AAAA,cACN,YAAY;AAAA,YAChB,CAAC;AACD,gBAAI,cAAc;AAAA,cACd,OAAO;AAAA,cACP,eAAe,eAAe,uBAAuB,kBAAkB,kBAAkB;AAAA,cACzF,SAAS;AACL,iCAAiB,SAAS;AAAA,kBACtB,MAAM;AAAA,kBACN,YAAY;AAAA,gBAChB,CAAC;AAAA,cACL;AAAA,YACJ;AACA,6BAAiB,QAAQ,QAAQ,YAAY,WAAW;AACxD,gBAAI,GAAG,SAAS;AACZ,+BAAiB,SAAS;AAAA,gBACtB,MAAM;AAAA,gBACN,iBAAiB,cAAc;AAAA,cACnC,CAAC;AAAA,YACL;AACA,6BAAiB,QAAQ,QAAQ,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,6BAA6B,SAAS,UAAU,gBAAgB,CAAC,GAAG,EAAE,WAAW,GAAG,WAAW,SAAS,GAAG,WAAW,MAAM,SAAS,QAAQ,QAAQ,CAAC,CAAC;AAChO,6BAAiB,QAAQ,QAAQ,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,WAAW,GAAG,EAAE,WAAW,GAAG,WAAW,MAAM,SAAS,QAAQ,QAAQ,CAAC,CAAC;AAAA,UAC/J;AAAA,QACJ,OACK;AACD,yBAAe,QAAQ,QAAQ,cAAc;AAAA,QACjD;AAAA,MACJ;AACA,WAAK,QAAQ;AAAA,IACjB;AACA,QAAI,EAAE,UAAU,IAAI;AACpB,QAAI,EAAE,QAAQ,IAAI,UAAU;AAC5B,QAAI,WAAW,KAAK,WAAW,IAAI,0BAA0B,SAAS,EAAE;AACxE,aAAS,QAAQ,WAAW,eAAc;AAC1C,aAAS,qBAAqB;AAC9B,aAAS,aAAa,YAAY,QAAQ;AAC1C,QAAI,cAAc,KAAK,cAAc,IAAI,YAAY,KAAK,UAAU,wBAAwB;AAC5F,gBAAY,mBAAmB,SAAS;AACxC,gBAAY,QAAQ,GAAG,eAAe,KAAK,iBAAiB;AAC5D,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,WAAW,KAAK,aAAa;AAAA,EACxD;AAAA,EACA,UAAU;AACN,SAAK,SAAS,QAAQ;AAAA,EAC1B;AAAA;AAAA,EAEA,YAAY,aAAa,OAAO;AAC5B,QAAI,iBAAiB,KAAK,UAAU;AACpC,QAAI,cAAc,KAAK;AAEvB,QAAI,eAAe,gBAAgB,aAAa;AAG5C,UAAI,gBAAgB,gBAAgB;AAChC,oBAAY,SAAS;AAAA,UACjB,MAAM;AAAA,UACN,OAAO;AAAA,YACH,gBAAgB,MAAM;AAAA,YACtB,eAAe,sBAAsB;AAAA,YACrC,SAAS;AAAA,UACb;AAAA,QACJ,CAAC;AAAA,MAEL,OACK;AACD,oBAAY,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAAA,MACrD;AAAA,IACJ;AACA,QAAI,aAAa;AACb,kBAAY,SAAS,EAAE,MAAM,kBAAkB,MAAM,CAAC;AAAA,IAC1D;AAAA,EACJ;AAAA,EACA,YAAY;AACR,QAAI,kBAAkB,KAAK,UAAU;AACrC,QAAI,EAAE,iBAAiB,IAAI;AAC3B,QAAI,kBAAkB;AAClB,uBAAiB,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAAA,IAC1D;AAEA,QAAI,oBAAoB,kBAAkB;AACtC,sBAAgB,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAAA,IACzD;AAAA,EACJ;AAAA,EACA,UAAU;AACN,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAAA,EACjC;AACJ;AAGA,cAAc,WAAW;AACzB,SAAS,qBAAqB,MAAM,MAAM,oBAAoB,WAAW;AACrE,MAAI,YAAY,KAAK;AACrB,MAAI,YAAY,KAAK;AACrB,MAAI,QAAQ,UAAU,MAAM;AAC5B,MAAI,QAAQ,UAAU,MAAM;AAC5B,MAAI,gBAAgB,CAAC;AACrB,MAAI,UAAU,WAAW,UAAU,QAAQ;AACvC,kBAAc,SAAS,UAAU;AACjC,kBAAc,SAAS,KAAK,QAAQ,QAAQ;AAC5C,QAAI,UAAU,QAAQ;AAGlB,cAAQ,WAAW,kBAAkB;AAAA,IACzC,OACK;AAGD,cAAQ;AAAA,IACZ;AAAA,EACJ;AACA,MAAI,QAAQ,UAAU,OAAO,OAAO,KAAK,QAAQ,SAAS,KAAK,gBAAgB,KAAK,cAChF,KAAK,YACL,IAAI;AACR,MAAI,MAAM,cAAc;AACpB,kBAAc,SAAS;AAAA,EAC3B;AACA,MAAI,WAAW;AAAA,IACX,YAAY;AAAA,IACZ;AAAA,EACJ;AACA,WAAS,YAAY,WAAW;AAC5B,aAAS,UAAU,MAAM,IAAI;AAAA,EACjC;AACA,SAAO;AACX;AACA,SAAS,uBAAuB,WAAW;AACvC,MAAI,EAAE,QAAQ,IAAI,UAAU;AAC5B,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,MAAM;AACf,YAAQ,QAAQ;AAAA,EACpB;AACA,SAAO;AACX;AAEA,IAAM,gBAAN,cAA4B,YAAY;AAAA,EACpC,YAAY,UAAU;AAClB,UAAM,QAAQ;AAEd,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAC7B,SAAK,oBAAoB,CAAC,OAAO;AAC7B,UAAI,EAAE,WAAAC,WAAU,IAAI;AACpB,UAAI,QAAQ,KAAK,WAAW,EAAE;AAC9B,UAAI,MAAM,SAAS,KAAK;AACxB,UAAI,aAAa,KAAK,aAAa,IAAI;AACvC,WAAK,SAAS,cAAcA,WAAU,QAAQ,QAAQ;AAEtD,WAAK,SAAS,cAAc,CAAC,KAAK,UAAU,iBAAiB,GAAG,UAAU,MAAM,KAC3E,GAAG,WAAW,KAAK,UAAU,MAAM,mBAAmB,WAAW,SAAS,UAAW;AAAA,IAC9F;AACA,SAAK,kBAAkB,CAAC,OAAO;AAC3B,UAAI,EAAE,QAAQ,IAAI,KAAK;AACvB,UAAI,aAAa,KAAK;AACtB,WAAK,iBAAiB,kBAAkB,QAAQ,eAAe,EAAE,YAAY,KAAK,WAAW,SAAS,UAAU;AAChH,UAAI,QAAQ,KAAK,WAAW,EAAE;AAC9B,WAAK,gBAAgB;AACrB,WAAK,cAAc,SAAS,KAAK;AACjC,cAAQ,YAAY,SAAS;AAC7B,cAAQ,QAAQ,QAAQ,oBAAoB;AAAA,QACxC,IAAI;AAAA,QACJ,OAAO,IAAI,UAAU,SAAS,WAAW,KAAK,WAAW,QAAQ;AAAA,QACjE,SAAS,GAAG;AAAA,QACZ,MAAM,QAAQ;AAAA,MAClB,CAAC;AAAA,IACL;AACA,SAAK,kBAAkB,CAAC,KAAK,SAAS,OAAO;AACzC,UAAI,EAAE,QAAQ,IAAI,KAAK;AACvB,UAAI,iBAAiB,KAAK;AAC1B,UAAI,aAAa,KAAK,YAAY;AAClC,UAAI,gBAAgB,KAAK,WAAW;AACpC,UAAI,WAAW;AACf,UAAI,wBAAwB;AAC5B,UAAI,YAAY;AAChB,UAAI,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,eAAe,sBAAsB;AAAA,QACrC,SAAS;AAAA,MACb;AACA,UAAI,KAAK;AACL,YAAI,aAAa,IAAI,gBAAgB,WAAW,eACzC,KAAK,qBACL,CAAC,KAAK,kBAAkB,YAAY,GAAG;AAC9C,YAAI,CAAC,YAAY;AACb,qBAAW,gBAAgB,YAAY,KAAK,GAAG,UAAU,UAAU,SAAS,wBAAwB,GAAG,cAAc,KAAK;AAAA,QAC9H;AAAA,MACJ;AACA,UAAI,UAAU;AACV,gCAAwB,0BAA0B,gBAAgB,QAAQ,eAAe,EAAE,cAAc,UAAU,OAAO;AAC1H,oBAAY,gBAAgB;AAC5B,YAAI,CAAC,mBAAmB,aAAa,IAAI,aAAa,OAAO,GAAG;AAC5D,sBAAY;AACZ,qBAAW;AACX,kCAAwB;AACxB,sBAAY,gBAAgB;AAAA,QAChC;AAAA,MACJ;AACA,UAAI,uBAAuB;AACvB,gBAAQ,SAAS;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACX,CAAC;AAAA,MACL,OACK;AACD,gBAAQ,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAAA,MACnD;AACA,UAAI,CAAC,WAAW;AACZ,qBAAa;AAAA,MACjB,OACK;AACD,sBAAc;AAAA,MAClB;AACA,UAAI,CAAC,SAAS;AACV,YAAI,YAAY,YAAY,YAAY,GAAG,GAAG;AAC1C,qBAAW;AAAA,QACf;AACA,aAAK,gBAAgB;AACrB,aAAK,wBAAwB;AAAA,MACjC;AAAA,IACJ;AACA,SAAK,gBAAgB,CAAC,OAAO;AACzB,UAAI,EAAE,QAAQ,IAAI,KAAK;AACvB,UAAI,WAAW,KAAK,WAAW;AAC/B,UAAI,gBAAgB,KAAK,WAAW;AACpC,UAAI,WAAW,IAAI,UAAU,SAAS,UAAU,aAAa;AAC7D,UAAI,iBAAiB,KAAK;AAC1B,UAAI,wBAAwB,KAAK;AACjC,cAAQ,QAAQ,QAAQ,mBAAmB;AAAA,QACvC,IAAI,KAAK;AAAA,QACT,OAAO;AAAA,QACP,SAAS,GAAG;AAAA,QACZ,MAAM,QAAQ;AAAA,MAClB,CAAC;AACD,UAAI,KAAK,eAAe;AACpB,YAAI,kBAAkB,IAAI,UAAU,SAAS,sBAAsB,KAAK,SAAS,KAAK,GAAG,gBAAgB,sBAAsB,UAAU,cAAc,UAAU,IAAI,IAAI;AACzK,gBAAQ,SAAS;AAAA,UACb,MAAM;AAAA,UACN,YAAY;AAAA,QAChB,CAAC;AACD,YAAI,iBAAiB;AAAA,UACjB,UAAU;AAAA,UACV,OAAO;AAAA,UACP,eAAe,eAAe,uBAAuB,SAAS,aAAa;AAAA,UAC3E,SAAS;AACL,oBAAQ,SAAS;AAAA,cACb,MAAM;AAAA,cACN,YAAY;AAAA;AAAA,YAChB,CAAC;AAAA,UACL;AAAA,QACJ;AACA,gBAAQ,QAAQ,QAAQ,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,cAAc,GAAG,EAAE,IAAI,KAAK,eAAe,YAAY,KAAK,cAAc,cAAc,eAAe,CAAC,GAAG,UAAU,KAAK,cAAc,YAAY,eAAe,CAAC,GAAG,SAAS,GAAG,WAAW,MAAM,QAAQ,QAAQ,CAAC,CAAC;AAC7R,gBAAQ,QAAQ,QAAQ,eAAe,cAAc;AAAA,MACzD,OACK;AACD,gBAAQ,QAAQ,QAAQ,gBAAgB;AAAA,MAC5C;AAEA,WAAK,cAAc;AACnB,WAAK,iBAAiB;AACtB,WAAK,gBAAgB;AAAA,IAEzB;AACA,QAAI,EAAE,UAAU,IAAI;AACpB,QAAI,WAAW,KAAK,WAAW,IAAI,0BAA0B,SAAS,EAAE;AACxE,aAAS,QAAQ,WAAW;AAC5B,aAAS,qBAAqB;AAC9B,aAAS,aAAa,YAAY,UAAU,QAAQ,QAAQ;AAC5D,QAAI,cAAc,KAAK,cAAc,IAAI,YAAY,KAAK,UAAU,2BAA2B,QAAQ,CAAC;AACxG,gBAAY,QAAQ,GAAG,eAAe,KAAK,iBAAiB;AAC5D,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,WAAW,KAAK,aAAa;AAAA,EACxD;AAAA,EACA,UAAU;AACN,SAAK,SAAS,QAAQ;AAAA,EAC1B;AAAA,EACA,WAAW,IAAI;AACX,WAAO,eAAe,GAAG,WAAW,WAAW;AAAA,EACnD;AACJ;AACA,SAAS,gBAAgB,MAAM,MAAM,aAAa,eAAe;AAC7D,MAAI,UAAU,KAAK,QAAQ;AAC3B,MAAI,QAAQ,KAAK,SAAS,MAAM;AAChC,MAAI,QAAQ,KAAK,SAAS,MAAM;AAChC,MAAI,QAAQ,UAAU,OAAO,OAAO,SAAS,KAAK,SAAS;AAC3D,MAAI,aAAa;AACb,QAAI,QAAQ,IAAI,cAAc,OAAO,KAAK,IAAI,cAAc,KAAK;AAC7D,aAAO,EAAE,YAAY,MAAM;AAAA,IAC/B;AAAA,EACJ,WACS,QAAQ,IAAI,cAAc,KAAK,KAAK,IAAI,cAAc,OAAO;AAClE,WAAO,EAAE,UAAU,MAAM;AAAA,EAC7B;AACA,SAAO;AACX;AAEA,IAAM,eAAN,MAAmB;AAAA,EACf,YAAY,SAAS;AACjB,SAAK,UAAU;AACf,SAAK,4BAA4B;AACjC,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,WAAW,CAAC,eAAe;AAC5B,UAAI,WAAW,SAAS;AACpB,aAAK,4BAA4B;AAAA,MACrC;AAAA,IACJ;AACA,SAAK,wBAAwB,CAAC,QAAQ;AAClC,UAAI,iBAAiB,KAAK,QAAQ,QAAQ;AAC1C,UAAI,SAAS,sBAAsB,IAAI,SAAS;AAChD,WAAK,gBAAgB,CAAC,CAAC,eAAe,QAAQ,cAAc;AAC5D,WAAK,eAAe,CAAC,CAAC,eAAe,QAAQ,cAAc,QAAQ;AAAA,IACvE;AACA,SAAK,sBAAsB,CAAC,QAAQ;AAChC,UAAI,EAAE,SAAAE,SAAQ,IAAI;AAClB,UAAI,EAAE,iBAAAC,iBAAgB,IAAI;AAC1B,UAAI,gBAAgBD,SAAQ,eAAe;AAE3C,UAAI,CAACC,iBAAgB,gBAAgB;AACjC,YAAI,cAAc;AAAA,QACd,CAAC,KAAK,2BACR;AACE,cAAI,eAAeD,SAAQ,QAAQ;AACnC,cAAI,iBAAiB,CAAC,gBAAgB,CAAC,KAAK,gBAAgB;AACxD,YAAAA,SAAQ,YAAY,SAAS,GAAG;AAAA,UACpC;AAAA,QACJ;AACA,YAAI,cAAc;AAAA,QACd,CAAC,KAAK,cACR;AACE,UAAAA,SAAQ,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAAA,QAC/C;AAAA,MACJ;AACA,WAAK,4BAA4B;AAAA,IACrC;AACA,QAAI,kBAAkB,KAAK,kBAAkB,IAAI,gBAAgB,QAAQ;AACzE,oBAAgB,mBAAmB;AACnC,oBAAgB,oBAAoB;AACpC,oBAAgB,QAAQ,GAAG,eAAe,KAAK,qBAAqB;AACpE,oBAAgB,QAAQ,GAAG,aAAa,KAAK,mBAAmB;AAIhE,YAAQ,QAAQ,GAAG,UAAU,KAAK,QAAQ;AAAA,EAC9C;AAAA,EACA,UAAU;AACN,SAAK,QAAQ,QAAQ,IAAI,UAAU,KAAK,QAAQ;AAChD,SAAK,gBAAgB,QAAQ;AAAA,EACjC;AACJ;AAEA,IAAM,kBAAkB;AAAA,EACpB,mBAAmB;AACvB;AACA,IAAM,oBAAoB;AAAA,EACtB,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,cAAc;AAAA,EACd,YAAY;AAChB;AAOA,IAAM,0BAAN,MAA8B;AAAA,EAC1B,YAAY,UAAU,kBAAkB;AACpC,SAAK,mBAAmB;AACxB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAChB,SAAK,kBAAkB,CAAC,OAAO;AAC3B,WAAK,WAAW,KAAK,cAAc,GAAG,SAAS;AAAA,IACnD;AACA,SAAK,kBAAkB,CAAC,KAAK,SAAS,OAAO;AACzC,UAAI,EAAE,UAAAH,UAAS,IAAI,KAAK;AACxB,UAAI,mBAAmB;AACvB,UAAI,iBAAiB;AACrB,UAAI,YAAY;AAChB,UAAI,cAAc;AAAA,QACd,gBAAgB,sBAAsB;AAAA,QACtC,eAAe,sBAAsB;AAAA,QACrC,SAAS,KAAK,SAAS;AAAA,MAC3B;AACA,UAAI,KAAK;AACL,2BAAmB,IAAI;AACvB,YAAI,KAAK,oBAAoB,GAAG,WAAW,gBAAgB,GAAG;AAC1D,2BAAiB,wBAAwB,IAAI,UAAU,KAAK,UAAU,gBAAgB;AACtF,sBAAY,gBAAgB,kBAAkB,cAAc;AAC5D,sBAAY,CAAC,mBAAmB,aAAa,IAAI,aAAa,gBAAgB;AAC9E,cAAI,WAAW;AACX,wBAAY,gBAAgB,sBAAsB;AAClD,6BAAiB;AAAA,UACrB;AAAA,QACJ;AAAA,MACJ;AACA,WAAK,YAAY,kBAAkB,WAAW;AAG9C,MAAAA,UAAS,mBAAmB,WAAW,CAAC,kBAAkB,CAAC,SAAS,cAAc,kBAAkB,CAAC;AACrG,UAAI,CAAC,WAAW;AACZ,qBAAa;AAAA,MACjB,OACK;AACD,sBAAc;AAAA,MAClB;AACA,UAAI,CAAC,SAAS;AACV,QAAAA,UAAS,qBAAqB,CAAC,cAAc;AAC7C,aAAK,mBAAmB;AACxB,aAAK,iBAAiB;AAAA,MAC1B;AAAA,IACJ;AACA,SAAK,gBAAgB,CAAC,QAAQ;AAC1B,UAAI,EAAE,kBAAkB,eAAe,IAAI;AAC3C,WAAK,UAAU;AACf,UAAI,oBAAoB,gBAAgB;AACpC,YAAI,WAAW,KAAK,YAAY;AAChC,YAAI,YAAY,SAAS,QAAQ;AACjC,YAAI,WAAW,KAAK;AACpB,yBAAiB,QAAQ,QAAQ,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,6BAA6B,SAAS,UAAU,gBAAgB,CAAC,GAAG,EAAE,WAAW,IAAI,WAAW,SAAS,IAAI,WAAW,MAAM,UAAU,CAAC,CAAC;AACnN,YAAI,SAAS,QAAQ;AACjB,cAAI,eAAe,kBAAkB,cAAc;AACnD,2BAAiB,SAAS;AAAA,YACtB,MAAM;AAAA,YACN,YAAY;AAAA,UAChB,CAAC;AACD,cAAI,IAAI,SAAS;AACb,6BAAiB,SAAS;AAAA,cACtB,MAAM;AAAA,cACN,iBAAiB,eAAe,SAAS;AAAA,YAC7C,CAAC;AAAA,UACL;AAEA,2BAAiB,QAAQ,QAAQ,gBAAgB;AAAA,YAC7C,OAAO,IAAI,UAAU,kBAAkB,eAAe,KAAK,eAAe,QAAQ;AAAA,YAClF,eAAe,CAAC;AAAA,YAChB,SAAS;AACL,+BAAiB,SAAS;AAAA,gBACtB,MAAM;AAAA,gBACN,YAAY;AAAA,cAChB,CAAC;AAAA,YACL;AAAA,YACA,WAAW,IAAI;AAAA,YACf,MAAM;AAAA,UACV,CAAC;AAAA,QACL;AAAA,MACJ;AACA,WAAK,mBAAmB;AACxB,WAAK,iBAAiB;AAAA,IAC1B;AACA,QAAI,cAAc,KAAK,cAAc,IAAI,YAAY,UAAU,wBAAwB;AACvF,gBAAY,iBAAiB;AAC7B,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,WAAW,KAAK,aAAa;AACpD,SAAK,mBAAmB;AAAA,EAC5B;AAAA,EACA,cAAc,WAAW;AACrB,QAAI,OAAO,KAAK,qBAAqB,UAAU;AAC3C,aAAO,cAAc,KAAK,gBAAgB;AAAA,IAC9C;AACA,QAAI,OAAO,KAAK,qBAAqB,YAAY;AAC7C,aAAO,cAAc,KAAK,iBAAiB,SAAS,CAAC;AAAA,IACzD;AACA,WAAO,kBAAkB,SAAS;AAAA,EACtC;AAAA,EACA,YAAY,aAAa,OAAO;AAC5B,QAAI,cAAc,KAAK;AACvB,QAAI,eAAe,gBAAgB,aAAa;AAC5C,kBAAY,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAAA,IACrD;AACA,QAAI,aAAa;AACb,kBAAY,SAAS,EAAE,MAAM,kBAAkB,MAAM,CAAC;AAAA,IAC1D;AAAA,EACJ;AAAA,EACA,YAAY;AACR,QAAI,KAAK,kBAAkB;AACvB,WAAK,iBAAiB,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAAA,IAC/D;AAAA,EACJ;AAAA,EACA,oBAAoB,IAAI,kBAAkB;AACtC,QAAI,aAAa,iBAAiB,QAAQ;AAC1C,QAAI,OAAO,eAAe,YAAY;AAClC,aAAO,WAAW,KAAK,iBAAiB,aAAa,EAAE;AAAA,IAC3D;AACA,QAAI,OAAO,eAAe,YAAY,YAAY;AAC9C,aAAO,QAAQ,eAAe,IAAI,UAAU,CAAC;AAAA,IACjD;AACA,WAAO;AAAA,EACX;AACJ;AAGA,SAAS,wBAAwB,UAAU,UAAU,SAAS;AAC1D,MAAI,WAAW,OAAO,OAAO,CAAC,GAAG,SAAS,aAAa;AACvD,WAAS,aAAa,QAAQ,YAAY,uBAAuB;AAC7D,WAAO,OAAO,UAAU,UAAU,UAAU,QAAQ,CAAC;AAAA,EACzD;AACA,MAAI,EAAE,SAAS,MAAM,IAAI,eAAe,UAAU,OAAO;AACzD,MAAI,MAAM;AAAA,IAAc;AAAA,IAAS;AAAA,IAAO,SAAS;AAAA,IAAU,SAAS;AAAA,IAAQ,QAAQ,QAAQ,sBAAsB,QAAQ,SAAS,QAAQ;AAAA;AAAA,IAC3I;AAAA,EAAO;AACP,MAAI,QAAQ,SAAS,MAAM;AAG3B,MAAI,SAAS,UAAU,SAAS,WAAW;AACvC,YAAQ,QAAQ,QAAQ,IAAI,OAAO,SAAS,SAAS;AAAA,EACzD;AACA,MAAI,MAAM,SAAS,WACf,QAAQ,QAAQ,IAAI,OAAO,SAAS,QAAQ,IAC5C,mBAAmB,SAAS,QAAQ,OAAO,OAAO;AACtD,MAAI,WAAW,oBAAoB,IAAI,OAAO,EAAE,OAAO,IAAI,CAAC;AAC5D,SAAO,EAAE,KAAK,SAAS;AAC3B;AAGA,SAAS,kBAAkB,IAAI;AAC3B,MAAI,MAAM,kBAAkB,IAAI,OAAO;AACvC,MAAI,MAAM,MACN,KAAK,MAAM,GAAG,IACd,EAAE,QAAQ,MAAM;AACpB,SAAO,cAAc,GAAG;AAC5B;AACA,OAAO,iBAAiB;AACxB,SAAS,kBAAkB,IAAI,MAAM;AACjC,MAAI,SAAS,OAAO;AACpB,MAAI,gBAAgB,SAAS,SAAS,MAAM,MAAM;AAClD,SAAO,GAAG,aAAa,UAAU,YAAY,KAAK;AACtD;AAOA,IAAM,oBAAN,MAAwB;AAAA,EACpB,YAAY,IAAI,WAAW,CAAC,GAAG;AAC3B,SAAK,oBAAoB,CAAC,OAAO;AAC7B,UAAI,EAAE,UAAAA,UAAS,IAAI;AACnB,UAAI,EAAE,aAAa,eAAe,IAAI,KAAK;AAC3C,MAAAA,UAAS,cACL,eAAe,OACX,cACC,GAAG,UAAU,IAAI,qBAAqB;AAC/C,MAAAA,UAAS,QACL,GAAG;AAAA;AAAA,QACE,kBAAkB,OAAO,iBAAiB,qBAAqB;AAAA,UAChE;AAAA,IACZ;AACA,SAAK,kBAAkB,CAAC,OAAO;AAC3B,UAAI,GAAG,WACH,KAAK,SAAS,SACd,GAAG,UAAU,UAAU,SAAS,UAAU,GAAG;AAC7C,aAAK,SAAS,OAAO,YAAY,EAAE,UAAU,IAAI,mBAAmB;AAAA,MACxE;AAAA,IACJ;AACA,SAAK,WAAW;AAChB,QAAI,WAAW,KAAK,WAAW,IAAI,0BAA0B,EAAE;AAC/D,aAAS,qBAAqB;AAC9B,QAAI,SAAS,gBAAgB,MAAM;AAC/B,eAAS,QAAQ,WAAW,SAAS;AAAA,IACzC;AACA,QAAI,SAAS,YAAY,MAAM;AAC3B,eAAS,OAAO,aAAa,SAAS;AAAA,IAC1C;AACA,aAAS,QAAQ,GAAG,eAAe,KAAK,iBAAiB;AACzD,aAAS,QAAQ,GAAG,aAAa,KAAK,eAAe;AACrD,QAAI,wBAAwB,UAAU,SAAS,SAAS;AAAA,EAC5D;AAAA,EACA,UAAU;AACN,SAAK,SAAS,QAAQ;AAAA,EAC1B;AACJ;AAQA,IAAM,0BAAN,cAAsC,gBAAgB;AAAA,EAClD,YAAY,aAAa;AACrB,UAAM,WAAW;AACjB,SAAK,mBAAmB;AACxB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB;AACvB,SAAK,oBAAoB,CAAC,OAAO;AAC7B,WAAK,QAAQ,QAAQ,eAAe,EAAE;AACtC,UAAI,CAAC,KAAK,kBAAkB;AAExB,aAAK,QAAQ,QAAQ,aAAa,EAAE;AAAA,MACxC;AAAA,IACJ;AACA,SAAK,oBAAoB,CAAC,OAAO;AAC7B,UAAI,CAAC,KAAK,kBAAkB;AACxB,aAAK,QAAQ,QAAQ,YAAY,EAAE;AAAA,MACvC;AAAA,IACJ;AACA,SAAK,kBAAkB,CAAC,OAAO;AAC3B,WAAK,QAAQ,QAAQ,aAAa,EAAE;AACpC,UAAI,CAAC,KAAK,kBAAkB;AAExB,aAAK,QAAQ,QAAQ,WAAW,EAAE;AAAA,MACtC;AAAA,IACJ;AACA,QAAI,UAAU,KAAK,UAAU,IAAI,gBAAgB,WAAW;AAC5D,YAAQ,QAAQ,GAAG,eAAe,KAAK,iBAAiB;AACxD,YAAQ,QAAQ,GAAG,eAAe,KAAK,iBAAiB;AACxD,YAAQ,QAAQ,GAAG,aAAa,KAAK,eAAe;AAAA,EACxD;AAAA,EACA,UAAU;AACN,SAAK,QAAQ,QAAQ;AAAA,EACzB;AAAA,EACA,cAAc,MAAM;AAChB,SAAK,mBAAmB;AAAA,EAC5B;AAAA,EACA,mBAAmB,MAAM;AACrB,QAAI,MAAM;AAGN,UAAI,KAAK,iBAAiB;AACtB,aAAK,gBAAgB,MAAM,aAAa;AACxC,aAAK,kBAAkB;AAAA,MAC3B;AAAA,IACJ,OACK;AACD,UAAI,WAAW,KAAK,iBAEd,SAAS,cAAc,KAAK,cAAc,IAC1C;AACN,UAAI,UAAU;AACV,aAAK,kBAAkB;AACvB,iBAAS,MAAM,aAAa;AAAA,MAChC;AAAA,IACJ;AAAA,EACJ;AACJ;AAMA,IAAM,sBAAN,MAA0B;AAAA,EACtB,YAAY,qBAAqB,UAAU;AACvC,QAAI,cAAc;AAClB;AAAA;AAAA,MAEA,wBAAwB,YACpB,+BAA+B;AAAA,MAAS;AACxC,oBAAc;AACd,iBAAW,YAAY,CAAC;AAAA,IAC5B,OACK;AACD,iBAAY,uBAAuB,CAAC;AAAA,IACxC;AACA,QAAI,WAAW,KAAK,WAAW,IAAI,wBAAwB,WAAW;AACtE,QAAI,OAAO,SAAS,iBAAiB,UAAU;AAC3C,eAAS,QAAQ,WAAW,SAAS;AAAA,IACzC,WACS,gBAAgB,UAAU;AAC/B,eAAS,QAAQ,WAAW;AAAA,IAChC;AACA,QAAI,OAAO,SAAS,mBAAmB,UAAU;AAC7C,eAAS,iBAAiB,SAAS;AAAA,IACvC;AACA,QAAI,mBAAmB,IAAI,wBAAwB,UAAU,SAAS,SAAS;AAG/E,qBAAiB,YAAY,oBAAoB;AAAA,EACrD;AAAA,EACA,UAAU;AACN,SAAK,SAAS,QAAQ;AAAA,EAC1B;AACJ;AAEA,IAAI,QAAQ,aAAa;AAAA,EACrB,MAAM;AAAA,EACN,uBAAuB,CAAC,cAAc,eAAe,eAAe,aAAa;AAAA,EACjF,sBAAsB,CAAC,YAAY;AAAA,EACnC,qBAAqB;AAAA,EACrB,gBAAgB;AAAA,EAChB,kBAAkB;AACtB,CAAC;", "names": ["dragging", "component", "options", "context", "documentPointer"]}